import { z } from 'zod'

// Validation schemas
export const MarkEntrySchema = z.object({
  studentId: z.string().min(1, 'Student ID is required'),
  examId: z.string().min(1, 'Exam ID is required'),
  obtainedMarks: z.number()
    .min(0, 'Marks cannot be negative')
    .max(1000, 'Marks cannot exceed 1000'), // Will be validated against exam maxMarks
  remarks: z.string().optional()
})

export const BulkMarkEntrySchema = z.object({
  examId: z.string().min(1, 'Exam ID is required'),
  marks: z.array(z.object({
    studentId: z.string().min(1, 'Student ID is required'),
    obtainedMarks: z.number()
      .min(0, 'Marks cannot be negative')
      .max(1000, 'Marks cannot exceed 1000'),
    remarks: z.string().optional()
  })).min(1, 'At least one mark entry is required')
})

// Validation functions
export interface ValidationError {
  field: string
  message: string
  studentId?: string
}

export interface MarkValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

export const validateMarkEntry = (
  studentId: string,
  examId: string,
  obtainedMarks: number,
  maxMarks: number,
  remarks?: string
): MarkValidationResult => {
  const errors: ValidationError[] = []

  // Basic validation
  if (!studentId || studentId.trim() === '') {
    errors.push({ field: 'studentId', message: 'Student ID is required' })
  }

  if (!examId || examId.trim() === '') {
    errors.push({ field: 'examId', message: 'Exam ID is required' })
  }

  // Marks validation
  if (obtainedMarks < 0) {
    errors.push({ 
      field: 'obtainedMarks', 
      message: 'Marks cannot be negative',
      studentId 
    })
  }

  if (obtainedMarks > maxMarks) {
    errors.push({ 
      field: 'obtainedMarks', 
      message: `Marks cannot exceed maximum marks (${maxMarks})`,
      studentId 
    })
  }

  // Check for decimal precision (max 2 decimal places)
  if (obtainedMarks % 0.01 !== 0) {
    errors.push({ 
      field: 'obtainedMarks', 
      message: 'Marks can have at most 2 decimal places',
      studentId 
    })
  }

  // Remarks validation (optional but if provided, should be reasonable length)
  if (remarks && remarks.length > 500) {
    errors.push({ 
      field: 'remarks', 
      message: 'Remarks cannot exceed 500 characters',
      studentId 
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateBulkMarkEntry = (
  examId: string,
  maxMarks: number,
  marksData: Array<{
    studentId: string
    obtainedMarks: number
    remarks?: string
  }>
): MarkValidationResult => {
  const errors: ValidationError[] = []

  // Exam validation
  if (!examId || examId.trim() === '') {
    errors.push({ field: 'examId', message: 'Exam ID is required' })
  }

  // Check if marks data is provided
  if (!marksData || marksData.length === 0) {
    errors.push({ field: 'marks', message: 'At least one mark entry is required' })
    return { isValid: false, errors }
  }

  // Validate each mark entry
  const studentIds = new Set<string>()
  marksData.forEach((mark, index) => {
    // Check for duplicate student IDs
    if (studentIds.has(mark.studentId)) {
      errors.push({ 
        field: 'studentId', 
        message: 'Duplicate student ID found',
        studentId: mark.studentId 
      })
    } else {
      studentIds.add(mark.studentId)
    }

    // Validate individual mark entry
    const validation = validateMarkEntry(
      mark.studentId,
      examId,
      mark.obtainedMarks,
      maxMarks,
      mark.remarks
    )

    // Add any errors from individual validation
    errors.push(...validation.errors)
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Grade calculation validation
export const validateGradeCalculation = (
  obtainedMarks: number,
  maxMarks: number
): { isValid: boolean; percentage?: number; error?: string } => {
  if (maxMarks <= 0) {
    return { isValid: false, error: 'Maximum marks must be greater than 0' }
  }

  if (obtainedMarks < 0) {
    return { isValid: false, error: 'Obtained marks cannot be negative' }
  }

  if (obtainedMarks > maxMarks) {
    return { isValid: false, error: 'Obtained marks cannot exceed maximum marks' }
  }

  const percentage = Math.round((obtainedMarks / maxMarks) * 100 * 100) / 100
  return { isValid: true, percentage }
}

// Error formatting utilities
export const formatValidationErrors = (errors: ValidationError[]): string => {
  if (errors.length === 0) return ''
  
  if (errors.length === 1) {
    return errors[0].message
  }

  return `Multiple errors found:\n${errors.map(e => `• ${e.message}`).join('\n')}`
}

export const groupErrorsByStudent = (errors: ValidationError[]): Record<string, ValidationError[]> => {
  return errors.reduce((acc, error) => {
    const key = error.studentId || 'general'
    if (!acc[key]) {
      acc[key] = []
    }
    acc[key].push(error)
    return acc
  }, {} as Record<string, ValidationError[]>)
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  STUDENT_ID: /^[a-zA-Z0-9-_]+$/,
  EXAM_ID: /^[a-zA-Z0-9-_]+$/,
  MARKS_FORMAT: /^\d+(\.\d{1,2})?$/, // Allows up to 2 decimal places
} as const

export const validatePattern = (value: string, pattern: RegExp, fieldName: string): ValidationError | null => {
  if (!pattern.test(value)) {
    return {
      field: fieldName,
      message: `Invalid ${fieldName} format`
    }
  }
  return null
}
