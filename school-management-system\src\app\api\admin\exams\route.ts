import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'terms' or 'exams'

    if (type === 'terms') {
      const terms = await prisma.term.findMany({
        orderBy: { createdAt: 'desc' }
      })
      return NextResponse.json(terms)
    } else if (type === 'exams') {
      const exams = await prisma.exam.findMany({
        include: {
          term: true,
          subject: true
        },
        orderBy: { date: 'desc' }
      })
      return NextResponse.json(exams)
    }

    return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 })
  } catch (error) {
    console.error('Error fetching exams/terms:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { type, data } = body

    if (type === 'term') {
      const term = await prisma.term.create({
        data: {
          name: data.name,
          startDate: new Date(data.startDate),
          endDate: new Date(data.endDate),
          academicYear: data.academicYear
        }
      })
      return NextResponse.json(term)
    } else if (type === 'exam') {
      const exam = await prisma.exam.create({
        data: {
          name: data.name,
          termId: data.termId,
          subjectId: data.subjectId,
          maxMarks: data.maxMarks,
          weightagePercent: data.weightagePercent,
          date: new Date(data.date)
        }
      })
      return NextResponse.json(exam)
    }

    return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 })
  } catch (error) {
    console.error('Error creating exam/term:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
