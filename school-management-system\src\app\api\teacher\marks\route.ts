import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import {
  MarkEntrySchema,
  BulkMarkEntrySchema,
  validateBulkMarkEntry,
  formatValidationErrors
} from '@/lib/marks-validation'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get teacher record for the logged-in user
    const teacher = await prisma.teacher.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const examId = searchParams.get('examId')
    const subjectId = searchParams.get('subjectId')
    const classId = searchParams.get('classId')

    const where: any = {
      gradedByTeacherId: teacher.id
    }
    
    if (examId && examId !== 'all') {
      where.examId = examId
    }
    if (subjectId && subjectId !== 'all') {
      where.exam = {
        subjectId: subjectId
      }
    }
    if (classId && classId !== 'all') {
      where.student = {
        currentClassId: classId
      }
    }

    const marks = await prisma.mark.findMany({
      where,
      include: {
        student: {
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        },
        exam: {
          include: {
            subject: true,
            term: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(marks)
  } catch (error) {
    console.error('Error fetching teacher marks:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get teacher record for the logged-in user
    const teacher = await prisma.teacher.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    const body = await request.json()
    
    // Check if this is a bulk operation
    if (body.marks && Array.isArray(body.marks)) {
      // Bulk marks entry
      const validation = BulkMarkEntrySchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json({
          error: 'Validation failed',
          details: validation.error.errors
        }, { status: 400 })
      }

      const { examId, marks: marksData } = validation.data

      // Validate that the exam exists and teacher can grade it
      const exam = await prisma.exam.findUnique({
        where: { id: examId },
        include: { subject: true }
      })

      if (!exam) {
        return NextResponse.json({ error: 'Exam not found' }, { status: 404 })
      }

      // Enhanced validation using our validation utility
      const bulkValidation = validateBulkMarkEntry(examId, exam.maxMarks, marksData)
      if (!bulkValidation.isValid) {
        return NextResponse.json({
          error: 'Validation failed',
          message: formatValidationErrors(bulkValidation.errors),
          details: bulkValidation.errors
        }, { status: 400 })
      }

      // Process bulk marks entry
      const results = []
      for (const markData of marksData) {
        try {
          // Check if marks already exist
          const existingMark = await prisma.mark.findUnique({
            where: {
              studentId_examId: {
                studentId: markData.studentId,
                examId
              }
            }
          })

          let mark
          if (existingMark) {
            // Update existing mark
            mark = await prisma.mark.update({
              where: { id: existingMark.id },
              data: {
                obtainedMarks: markData.obtainedMarks,
                remarks: markData.remarks,
                gradedByTeacherId: teacher.id
              }
            })
          } else {
            // Create new mark
            mark = await prisma.mark.create({
              data: {
                studentId: markData.studentId,
                examId,
                obtainedMarks: markData.obtainedMarks,
                remarks: markData.remarks,
                gradedByTeacherId: teacher.id
              }
            })
          }
          results.push({ success: true, mark })
        } catch (error) {
          results.push({ 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error',
            studentId: markData.studentId
          })
        }
      }

      return NextResponse.json({ results })
    } else {
      // Single mark entry
      const validation = MarkEntrySchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json({
          error: 'Validation failed',
          details: validation.error.errors
        }, { status: 400 })
      }

      const { studentId, examId, obtainedMarks, remarks } = validation.data

      // Validate that the exam exists and teacher can grade it
      const exam = await prisma.exam.findUnique({
        where: { id: examId },
        include: { subject: true }
      })

      if (!exam) {
        return NextResponse.json({ error: 'Exam not found' }, { status: 404 })
      }

      // Enhanced validation using our validation utility
      const { validateMarkEntry } = await import('@/lib/marks-validation')
      const markValidation = validateMarkEntry(studentId, examId, obtainedMarks, exam.maxMarks, remarks)
      if (!markValidation.isValid) {
        return NextResponse.json({
          error: 'Validation failed',
          message: formatValidationErrors(markValidation.errors),
          details: markValidation.errors
        }, { status: 400 })
      }

      // Check if marks already exist for this student-exam combination
      const existingMark = await prisma.mark.findUnique({
        where: {
          studentId_examId: {
            studentId,
            examId
          }
        }
      })

      let mark
      if (existingMark) {
        // Update existing mark
        mark = await prisma.mark.update({
          where: { id: existingMark.id },
          data: {
            obtainedMarks,
            remarks,
            gradedByTeacherId: teacher.id
          }
        })
      } else {
        // Create new mark
        mark = await prisma.mark.create({
          data: {
            studentId,
            examId,
            obtainedMarks,
            remarks,
            gradedByTeacherId: teacher.id
          }
        })
      }

      return NextResponse.json(mark)
    }
  } catch (error) {
    console.error('Error creating/updating mark:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
