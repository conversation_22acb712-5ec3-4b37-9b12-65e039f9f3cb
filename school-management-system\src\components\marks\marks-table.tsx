'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users,
  CheckCircle,
  AlertCircle,
  Eye,
  Edit
} from 'lucide-react'

interface Student {
  id: string
  admissionNo: string
  rollNumber: string
  firstName: string
  lastName: string
  email: string
  className: string
  sectionName: string
  currentMark: {
    id: string
    obtainedMarks: number
    remarks?: string
    createdAt: string
    updatedAt: string
  } | null
  hasMarks: boolean
}

interface Exam {
  id: string
  name: string
  maxMarks: number
  date: string
  subject: {
    id: string
    name: string
    code: string
    class: {
      id: string
      name: string
    }
  }
  term: {
    id: string
    name: string
  }
}

interface MarksTableProps {
  exam: Exam
  students: Student[]
  onEdit?: (examId: string) => void
  showActions?: boolean
}

export default function MarksTable({ exam, students, onEdit, showActions = true }: MarksTableProps) {
  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-100 text-green-800'
    if (percentage >= 80) return 'bg-blue-100 text-blue-800'
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800'
    if (percentage >= 60) return 'bg-orange-100 text-orange-800'
    return 'bg-red-100 text-red-800'
  }

  const getGrade = (percentage: number) => {
    if (percentage >= 90) return 'A+'
    if (percentage >= 80) return 'A'
    if (percentage >= 70) return 'B+'
    if (percentage >= 60) return 'B'
    if (percentage >= 50) return 'C+'
    if (percentage >= 40) return 'C'
    if (percentage >= 30) return 'D'
    return 'F'
  }

  const studentsWithMarks = students.filter(s => s.hasMarks)
  const studentsWithoutMarks = students.filter(s => !s.hasMarks)
  
  const stats = {
    totalStudents: students.length,
    gradedStudents: studentsWithMarks.length,
    pendingStudents: studentsWithoutMarks.length,
    averageMarks: studentsWithMarks.length > 0 
      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + (s.currentMark?.obtainedMarks || 0), 0) / studentsWithMarks.length * 100) / 100
      : 0,
    averagePercentage: studentsWithMarks.length > 0 
      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + ((s.currentMark?.obtainedMarks || 0) / exam.maxMarks * 100), 0) / studentsWithMarks.length * 100) / 100
      : 0
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Student Marks</span>
          </div>
          {showActions && onEdit && (
            <Button variant="outline" onClick={() => onEdit(exam.id)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit Marks
            </Button>
          )}
        </CardTitle>
        <CardDescription>
          Marks for {exam.name} - {exam.subject.name} ({exam.subject.class.name})
        </CardDescription>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="font-semibold text-gray-900">{stats.totalStudents}</div>
            <div className="text-gray-600">Total</div>
          </div>
          <div className="text-center p-2 bg-green-50 rounded">
            <div className="font-semibold text-green-800">{stats.gradedStudents}</div>
            <div className="text-green-600">Graded</div>
          </div>
          <div className="text-center p-2 bg-orange-50 rounded">
            <div className="font-semibold text-orange-800">{stats.pendingStudents}</div>
            <div className="text-orange-600">Pending</div>
          </div>
          <div className="text-center p-2 bg-blue-50 rounded">
            <div className="font-semibold text-blue-800">{stats.averageMarks}</div>
            <div className="text-blue-600">Avg Marks</div>
          </div>
          <div className="text-center p-2 bg-blue-50 rounded">
            <div className="font-semibold text-blue-800">{stats.averagePercentage}%</div>
            <div className="text-blue-600">Avg %</div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {students.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No students found for this exam</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Roll No.
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Section
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Percentage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Grade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Remarks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {students.map((student) => {
                    const percentage = student.currentMark 
                      ? Math.round((student.currentMark.obtainedMarks / exam.maxMarks) * 100 * 100) / 100
                      : 0
                    const grade = student.currentMark ? getGrade(percentage) : '-'
                    
                    return (
                      <tr key={student.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {student.firstName} {student.lastName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.admissionNo}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.rollNumber || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.sectionName || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.currentMark 
                            ? `${student.currentMark.obtainedMarks}/${exam.maxMarks}`
                            : '-'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.currentMark ? `${percentage}%` : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {student.currentMark ? (
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(percentage)}`}>
                              {grade}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.currentMark?.remarks || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {student.hasMarks ? (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Graded
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Pending
                            </span>
                          )}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="lg:hidden space-y-4">
              {students.map((student) => {
                const percentage = student.currentMark 
                  ? Math.round((student.currentMark.obtainedMarks / exam.maxMarks) * 100 * 100) / 100
                  : 0
                const grade = student.currentMark ? getGrade(percentage) : '-'
                
                return (
                  <Card key={student.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {student.firstName} {student.lastName}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {student.admissionNo} • Roll: {student.rollNumber || '-'} • Section: {student.sectionName || '-'}
                          </p>
                        </div>
                        <div className="ml-4">
                          {student.hasMarks ? (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Graded
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Pending
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {student.currentMark ? (
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Marks:</span>
                            <p className="text-gray-600">{student.currentMark.obtainedMarks}/{exam.maxMarks}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Percentage:</span>
                            <p className="text-gray-600 font-semibold">{percentage}%</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Grade:</span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(percentage)}`}>
                              {grade}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Remarks:</span>
                            <p className="text-gray-600">{student.currentMark.remarks || '-'}</p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-4 text-gray-500">
                          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                          <p>No marks entered yet</p>
                        </div>
                      )}
                    </div>
                  </Card>
                )
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
