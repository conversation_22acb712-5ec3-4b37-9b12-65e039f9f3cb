import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkClasses() {
  console.log('🔍 Checking classes and sections...')
  
  try {
    const classes = await prisma.class.findMany({
      include: {
        sections: true
      }
    })

    console.log(`\n📊 Found ${classes.length} classes:`)
    
    classes.forEach((cls, index) => {
      console.log(`${index + 1}. Class: ${cls.name} (ID: ${cls.id})`)
      cls.sections.forEach((section, sIndex) => {
        console.log(`   Section ${sIndex + 1}: ${section.name} (ID: ${section.id})`)
      })
    })

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkClasses()
