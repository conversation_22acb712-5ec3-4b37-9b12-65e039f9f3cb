'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

export default function HomePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role) {
      if (session.user.role === 'ADMIN') {
        router.push('/admin')
      } else if (session.user.role === 'TEACHER') {
        router.push('/teacher')
      } else if (session.user.role === 'STUDENT') {
        router.push('/student')
      }
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center px-4 sm:px-6">
          <CardTitle className="text-2xl sm:text-3xl font-bold leading-tight">
            Welcome to School Management System
          </CardTitle>
          <CardDescription className="text-sm sm:text-base mt-2">
            A comprehensive platform for managing school operations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 px-4 sm:px-6">
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm sm:text-base">
              Please sign in to access your dashboard
            </p>
            <Link href="/login">
              <Button className="w-full min-h-[44px] text-base">
                Sign In
              </Button>
            </Link>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h3 className="font-semibold mb-3 text-sm sm:text-base">Demo Accounts:</h3>
            <div className="text-xs sm:text-sm space-y-2">
              <div className="flex flex-col sm:flex-row sm:justify-between">
                <strong className="text-blue-600">Admin:</strong>
                <span className="text-gray-600 dark:text-gray-400"><EMAIL> / Admin@12345</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:justify-between">
                <strong className="text-green-600">Teacher:</strong>
                <span className="text-gray-600 dark:text-gray-400"><EMAIL> / Teacher@12345</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:justify-between">
                <strong className="text-purple-600">Student:</strong>
                <span className="text-gray-600 dark:text-gray-400"><EMAIL> / Student@12345</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
