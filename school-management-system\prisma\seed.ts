import { PrismaClient, UserRole, Gender, AttendanceStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  await prisma.auditLog.deleteMany()
  await prisma.reportCard.deleteMany()
  await prisma.mark.deleteMany()
  await prisma.attendance.deleteMany()
  await prisma.exam.deleteMany()
  await prisma.term.deleteMany()
  await prisma.enrollment.deleteMany()
  await prisma.student.deleteMany()
  await prisma.teacher.deleteMany()
  await prisma.subject.deleteMany()
  await prisma.section.deleteMany()
  await prisma.class.deleteMany()
  await prisma.user.deleteMany()
  await prisma.setting.deleteMany()

  console.log('🗑️  Cleared existing data')

  // Create admin user
  const adminPassword = await bcrypt.hash('Admin@12345', 12)
  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      hashedPassword: adminPassword,
      role: UserRole.ADMIN,
      firstName: 'School',
      lastName: 'Administrator',
      phone: '+1234567890'
    }
  })

  console.log('👨‍💼 Created admin user')

  // Create teachers
  const teacher1Password = await bcrypt.hash('Teacher@12345', 12)
  const teacher1User = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      hashedPassword: teacher1Password,
      role: UserRole.TEACHER,
      firstName: 'John',
      lastName: 'Smith',
      phone: '+1234567891'
    }
  })

  const teacher1 = await prisma.teacher.create({
    data: {
      userId: teacher1User.id,
      employeeCode: 'T001',
      qualification: 'M.Ed Mathematics',
      phoneAlt: '+1234567892',
      joinedOn: new Date('2023-06-01')
    }
  })

  const teacher2Password = await bcrypt.hash('Teacher@12345', 12)
  const teacher2User = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      hashedPassword: teacher2Password,
      role: UserRole.TEACHER,
      firstName: 'Sarah',
      lastName: 'Johnson',
      phone: '+1234567893'
    }
  })

  const teacher2 = await prisma.teacher.create({
    data: {
      userId: teacher2User.id,
      employeeCode: 'T002',
      qualification: 'M.Sc Physics',
      phoneAlt: '+1234567894',
      joinedOn: new Date('2023-06-01')
    }
  })

  console.log('👩‍🏫 Created teachers')

  // Create classes
  const grade8 = await prisma.class.create({
    data: {
      name: 'Grade 8'
    }
  })

  console.log('📚 Created Grade 8 class')

  // Create sections
  const sectionA = await prisma.section.create({
    data: {
      name: 'A',
      classId: grade8.id
    }
  })

  const sectionB = await prisma.section.create({
    data: {
      name: 'B',
      classId: grade8.id
    }
  })

  console.log('📋 Created sections A and B')

  // Create subjects
  const subjects = await Promise.all([
    prisma.subject.create({
      data: {
        name: 'Mathematics',
        code: 'MATH8',
        classId: grade8.id
      }
    }),
    prisma.subject.create({
      data: {
        name: 'English',
        code: 'ENG8',
        classId: grade8.id
      }
    }),
    prisma.subject.create({
      data: {
        name: 'Science',
        code: 'SCI8',
        classId: grade8.id
      }
    }),
    prisma.subject.create({
      data: {
        name: 'Social Studies',
        code: 'SOC8',
        classId: grade8.id
      }
    }),
    prisma.subject.create({
      data: {
        name: 'Computer Science',
        code: 'CS8',
        classId: grade8.id
      }
    })
  ])

  console.log('📖 Created 5 subjects')

  // Create students
  const students = []
  for (let i = 1; i <= 10; i++) {
    const studentPassword = await bcrypt.hash('Student@12345', 12)
    const studentUser = await prisma.user.create({
      data: {
        email: `student${i}@school.test`,
        hashedPassword: studentPassword,
        role: UserRole.STUDENT,
        firstName: `Student${i}`,
        lastName: `Last${i}`,
        phone: `+1234567${i.toString().padStart(3, '0')}`
      }
    })

    const section = i <= 5 ? sectionA : sectionB
    const rollNumber = i <= 5 ? `8A${i.toString().padStart(2, '0')}` : `8B${(i-5).toString().padStart(2, '0')}`

    const student = await prisma.student.create({
      data: {
        userId: studentUser.id,
        admissionNo: `ADM${i.toString().padStart(4, '0')}`,
        dob: new Date(2010, 5, i), // June 2010
        gender: i % 2 === 0 ? Gender.MALE : Gender.FEMALE,
        address: `${i} Main Street, City`,
        guardianName: `Guardian${i}`,
        guardianPhone: `+1234567${i.toString().padStart(3, '0')}`,
        currentClassId: grade8.id,
        currentSectionId: section.id,
        rollNumber
      }
    })

    students.push(student)

    // Create enrollment
    await prisma.enrollment.create({
      data: {
        studentId: student.id,
        classId: grade8.id,
        sectionId: section.id,
        academicYear: '2024-2025',
        active: true
      }
    })
  }

  console.log('👨‍🎓 Created 10 students')

  // Create term
  const term1 = await prisma.term.create({
    data: {
      name: 'Term 1',
      startDate: new Date('2024-06-01'),
      endDate: new Date('2024-09-30'),
      academicYear: '2024-2025'
    }
  })

  console.log('📅 Created Term 1')

  // Create exams
  const exams = await Promise.all([
    prisma.exam.create({
      data: {
        name: 'Unit Test 1',
        termId: term1.id,
        subjectId: subjects[0].id, // Mathematics
        maxMarks: 25,
        weightagePercent: 0.3,
        date: new Date('2024-07-15')
      }
    }),
    prisma.exam.create({
      data: {
        name: 'Mid Term',
        termId: term1.id,
        subjectId: subjects[0].id, // Mathematics
        maxMarks: 50,
        weightagePercent: 0.7,
        date: new Date('2024-08-15')
      }
    }),
    prisma.exam.create({
      data: {
        name: 'Unit Test 1',
        termId: term1.id,
        subjectId: subjects[1].id, // English
        maxMarks: 25,
        weightagePercent: 0.3,
        date: new Date('2024-07-16')
      }
    }),
    prisma.exam.create({
      data: {
        name: 'Mid Term',
        termId: term1.id,
        subjectId: subjects[1].id, // English
        maxMarks: 50,
        weightagePercent: 0.7,
        date: new Date('2024-08-16')
      }
    })
  ])

  console.log('📝 Created exams')

  // Create attendance for last 7 days
  const attendanceDates = []
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    attendanceDates.push(date)
  }

  for (const date of attendanceDates) {
    for (const student of students) {
      const status = Math.random() > 0.1 ? AttendanceStatus.PRESENT : 
                    Math.random() > 0.5 ? AttendanceStatus.LATE : 
                    Math.random() > 0.7 ? AttendanceStatus.HALF_DAY : AttendanceStatus.ABSENT

      await prisma.attendance.create({
        data: {
          studentId: student.id,
          date,
          status,
          remarks: status !== AttendanceStatus.PRESENT ? 'Reason noted' : null,
          takenByTeacherId: teacher1.id,
          classId: grade8.id,
          sectionId: student.currentSectionId!,
          subjectId: subjects[0].id // Mathematics
        }
      })
    }
  }

  console.log('📊 Created attendance records for last 7 days')

  // Create random marks
  for (const exam of exams) {
    for (const student of students) {
      const obtainedMarks = Math.floor(Math.random() * exam.maxMarks) + Math.floor(exam.maxMarks * 0.6) // 60-100% range
      
      await prisma.mark.create({
        data: {
          studentId: student.id,
          examId: exam.id,
          obtainedMarks,
          gradedByTeacherId: teacher1.id,
          remarks: obtainedMarks >= exam.maxMarks * 0.8 ? 'Excellent work!' : 
                   obtainedMarks >= exam.maxMarks * 0.6 ? 'Good effort' : 'Needs improvement'
        }
      })
    }
  }

  console.log('📊 Created random marks')

  // Create settings
  await prisma.setting.create({
    data: {
      key: 'grade_config',
      value: {
        A_PLUS: 90,
        A: 80,
        B_PLUS: 70,
        B: 60,
        C: 50,
        D: 40,
        E: 0
      }
    }
  })

  await prisma.setting.create({
    data: {
      key: 'school_info',
      value: {
        name: 'Advance School',
        address: '123 Education Street, City, State 12345',
        phone: '******-567-8900',
        email: '<EMAIL>',
        website: 'www.advanceschool.edu'
      }
    }
  })

  console.log('⚙️  Created system settings')

  console.log('✅ Database seeding completed successfully!')
  console.log('\n📋 Login Credentials:')
  console.log('Admin: <EMAIL> / Admin@12345')
  console.log('Teacher 1: <EMAIL> / Teacher@12345')
  console.log('Teacher 2: <EMAIL> / Teacher@12345')
  console.log('Student 1: <EMAIL> / Student@12345')
  console.log('... (and so on for students 2-10)')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
