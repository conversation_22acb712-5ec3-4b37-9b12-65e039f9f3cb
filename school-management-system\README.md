# School Management System

A comprehensive, production-ready School Management System built with Next.js 14, TypeScript, Prisma, and PostgreSQL.

## 🚀 Features

- **Role-Based Access Control**: Admin, Teacher, and Student portals
- **Student Management**: Complete student profiles, enrollment, and tracking
- **Teacher Portal**: Attendance tracking, marks entry, and class reports
- **Admin Dashboard**: Full system management with analytics
- **Attendance System**: Daily attendance tracking with status options
- **Marks Management**: Exam marks entry with validation and grading
- **Report Generation**: PDF report cards with professional templates
- **Analytics**: Charts and insights for attendance and performance
- **Audit Logging**: Complete system activity tracking

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **UI**: Tailwind CSS + shadcn/ui + Lucide React icons
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with credentials
- **Validation**: Zod
- **Charts**: Recharts
- **PDF Generation**: <PERSON>wright (headless Chromium)
- **Email**: Nodemailer
- **Testing**: Vitest + Playwright
- **Package Manager**: pnpm

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL database
- pnpm package manager

## 🚀 Getting Started

### 1. Clone and Install Dependencies

```bash
# Navigate to project directory
cd school-management-system

# Install dependencies
pnpm install
```

### 2. Environment Setup

```bash
# Copy environment file
cp .env.example .env

# Edit .env with your database credentials
# DATABASE_URL="postgresql://username:password@localhost:5432/school_management_system"
# NEXTAUTH_SECRET="your-secret-key-here"
```

### 3. Database Setup

```bash
# Run database migrations
pnpm prisma:migrate

# Seed the database with sample data
pnpm db:seed
```

### 4. Start Development Server

```bash
# Start the development server
pnpm dev
```

### 5. Access the Application

Visit [http://localhost:3000](http://localhost:3000)

## 👥 Default Login Credentials

After seeding the database, you can log in with:

### Admin Access
- **Email**: <EMAIL>
- **Password**: Admin@12345

### Teacher Access
- **Email**: <EMAIL>
- **Password**: Teacher@12345
- **Email**: <EMAIL>
- **Password**: Teacher@12345

### Student Access
- **Email**: <EMAIL>
- **Password**: Student@12345
- **Email**: <EMAIL>
- **Password**: Student@12345
- ... (<NAME_EMAIL>)

## 📁 Project Structure

```
src/
├── app/
│   ├── (auth)/           # Authentication pages
│   ├── (dash)/           # Dashboard pages (admin/teacher/student)
│   ├── (reports)/        # Report templates
│   ├── api/              # API routes
│   └── globals.css       # Global styles
├── components/           # Reusable UI components
├── lib/                  # Utility functions
│   ├── db.ts            # Database connection
│   ├── rbac.ts          # Role-based access control
│   └── grading.ts       # Grading calculations
└── types/               # TypeScript type definitions

prisma/
├── schema.prisma        # Database schema
└── seed.ts             # Database seeding

scripts/
└── generate-report-cards.ts  # CLI for report generation
```

## 🎯 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm prisma:migrate` - Run database migrations
- `pnpm prisma:studio` - Open Prisma Studio
- `pnpm db:seed` - Seed database with sample data
- `pnpm test` - Run unit tests
- `pnpm test:e2e` - Run end-to-end tests
- `pnpm reports:generate` - Generate report cards via CLI

## 🔐 Authentication & Authorization

The system uses NextAuth.js with role-based access control:

- **ADMIN**: Full system access
- **TEACHER**: Manage assigned classes, attendance, and marks
- **STUDENT**: View personal data and reports

## 📊 Database Schema

The system includes comprehensive data models:

- **Users**: Authentication and basic user info
- **Students**: Extended student profiles
- **Teachers**: Teacher-specific information
- **Classes & Sections**: Academic structure
- **Subjects**: Course offerings
- **Attendance**: Daily attendance records
- **Marks**: Exam results and grading
- **Terms & Exams**: Academic periods
- **Report Cards**: Generated PDF reports
- **Audit Logs**: System activity tracking

## 🎨 UI Components

Built with shadcn/ui components:
- Tables with sorting and pagination
- Forms with validation
- Modals and dialogs
- Charts and analytics
- Responsive design

## 📈 Features by Role

### Admin Portal (`/admin`)
- Dashboard with system overview
- Student and teacher management
- Class and subject administration
- Attendance and marks overview
- Report generation
- System settings
- Audit logs

### Teacher Portal (`/teacher`)
- Dashboard with assigned classes
- Attendance entry and management
- Marks entry with validation
- Class reports and analytics
- Student profiles (limited access)

### Student Portal (`/student`)
- Personal dashboard
- Attendance history and statistics
- Marks and grades overview
- Report card downloads
- Academic progress tracking

## 🔧 Configuration

### Environment Variables

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/sms"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Email (Development)
SMTP_HOST="localhost"
SMTP_PORT="1025"
SMTP_USER=""
SMTP_PASS=""

# File Storage
STORAGE_DEST="local"
```

### Grade Configuration

Grades are calculated based on percentage:
- A+: ≥ 90%
- A: ≥ 80%
- B+: ≥ 70%
- B: ≥ 60%
- C: ≥ 50%
- D: ≥ 40%
- E: < 40%

## 🧪 Testing

```bash
# Run unit tests
pnpm test

# Run end-to-end tests
pnpm test:e2e
```

## 📦 Deployment

### Docker Deployment

```bash
# Build and run with Docker
docker-compose up -d
```

### Vercel Deployment

1. Connect your repository to Vercel
2. Set environment variables
3. Deploy automatically on push

### Database Deployment

- **Development**: Local PostgreSQL
- **Production**: Neon, Railway, or any PostgreSQL provider

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code comments

---

**Built with ❤️ for educational institutions**
