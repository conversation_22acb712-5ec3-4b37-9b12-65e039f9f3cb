import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get teacher record for the logged-in user
    const teacher = await prisma.teacher.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const subjectId = searchParams.get('subjectId')
    const termId = searchParams.get('termId')

    const where: any = {}
    
    if (subjectId && subjectId !== 'all') {
      where.subjectId = subjectId
    }
    if (termId && termId !== 'all') {
      where.termId = termId
    }

    // Get all exams (teachers can potentially grade any exam)
    const exams = await prisma.exam.findMany({
      where,
      include: {
        subject: {
          include: {
            class: true
          }
        },
        term: true,
        _count: {
          select: {
            marks: true
          }
        }
      },
      orderBy: [
        { date: 'desc' },
        { createdAt: 'desc' }
      ]
    })

    return NextResponse.json(exams)
  } catch (error) {
    console.error('Error fetching exams:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
