'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { loginUser, logoutUser, type LoginCredentials, type LoginResult } from '@/lib/auth-utils'

interface UseLoginState {
  isLoading: boolean
  error: string | null
  success: boolean
}

interface UseLoginReturn extends UseLoginState {
  login: (credentials: LoginCredentials) => Promise<LoginResult>
  logout: (redirectUrl?: string) => Promise<void>
  clearError: () => void
  clearSuccess: () => void
  reset: () => void
}

/**
 * Custom hook for handling login functionality
 */
export function useLogin(): UseLoginReturn {
  const router = useRouter()
  const [state, setState] = useState<UseLoginState>({
    isLoading: false,
    error: null,
    success: false
  })

  const login = useCallback(async (credentials: LoginCredentials): Promise<LoginResult> => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      success: false
    }))

    try {
      const result = await loginUser(credentials)

      if (result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          success: true
        }))

        // Redirect to dashboard
        if (result.redirectUrl) {
          router.push(result.redirectUrl)
        }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Login failed'
        }))
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))

      return {
        success: false,
        error: errorMessage
      }
    }
  }, [router])

  const logout = useCallback(async (redirectUrl: string = '/login'): Promise<void> => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }))

    try {
      await logoutUser(redirectUrl)
      setState(prev => ({
        ...prev,
        isLoading: false,
        success: true
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Logout failed'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
    }
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null
    }))
  }, [])

  const clearSuccess = useCallback(() => {
    setState(prev => ({
      ...prev,
      success: false
    }))
  }, [])

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      success: false
    })
  }, [])

  return {
    ...state,
    login,
    logout,
    clearError,
    clearSuccess,
    reset
  }
}

/**
 * Hook for form-based login with validation
 */
export function useLoginForm() {
  const { login, ...loginState } = useLogin()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [validationErrors, setValidationErrors] = useState<{
    email?: string
    password?: string
  }>({})

  const updateField = useCallback((field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: undefined
      }))
    }

    // Clear login error when user starts typing
    if (loginState.error) {
      loginState.clearError()
    }
  }, [validationErrors, loginState])

  const validateForm = useCallback((): boolean => {
    const errors: typeof validationErrors = {}

    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!formData.email.includes('@')) {
      errors.email = 'Please enter a valid email address'
    }

    if (!formData.password.trim()) {
      errors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters long'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }, [formData])

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault()
    }

    if (!validateForm()) {
      return { success: false, error: 'Please fix the validation errors' }
    }

    return await login(formData)
  }, [formData, validateForm, login])

  const resetForm = useCallback(() => {
    setFormData({
      email: '',
      password: ''
    })
    setValidationErrors({})
    loginState.reset()
  }, [loginState])

  const fillDemoCredentials = useCallback((role: 'admin' | 'teacher' | 'student') => {
    const credentials = {
      admin: { email: '<EMAIL>', password: 'Admin@12345' },
      teacher: { email: '<EMAIL>', password: 'Teacher@12345' },
      student: { email: '<EMAIL>', password: 'Student@12345' }
    }

    setFormData(credentials[role])
    setValidationErrors({})
  }, [])

  return {
    ...loginState,
    formData,
    validationErrors,
    updateField,
    validateForm,
    handleSubmit,
    resetForm,
    fillDemoCredentials
  }
}
