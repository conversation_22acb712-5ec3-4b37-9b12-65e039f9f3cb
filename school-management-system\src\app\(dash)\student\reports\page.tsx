'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { studentNavigation } from '@/lib/navigation'
import {
  Calendar,
  FileText,
  Download,
  Award,
  TrendingUp,
  Eye,
  Printer
} from 'lucide-react'

interface ReportCard {
  id: string
  termName: string
  academicYear: string
  totalMarks: number
  obtainedMarks: number
  percentage: number
  grade: string
  rank: number
  generatedAt: string
  status: 'GENERATED' | 'PUBLISHED'
}



export default function StudentReportsPage() {
  const { data: session } = useSession()
  const [reportCards, setReportCards] = useState<ReportCard[]>([])

  // Mock data - in real app, this would come from API
  useEffect(() => {
    setReportCards([
      {
        id: '1',
        termName: 'Term 1',
        academicYear: '2024-2025',
        totalMarks: 500,
        obtainedMarks: 425,
        percentage: 85,
        grade: 'A',
        rank: 3,
        generatedAt: '2024-12-15',
        status: 'PUBLISHED'
      },
      {
        id: '2',
        termName: 'Term 2',
        academicYear: '2024-2025',
        totalMarks: 500,
        obtainedMarks: 380,
        percentage: 76,
        grade: 'B+',
        rank: 8,
        generatedAt: '2024-12-15',
        status: 'GENERATED'
      }
    ])
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'GENERATED':
        return 'bg-blue-100 text-blue-800'
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
        return 'bg-green-100 text-green-800'
      case 'A':
        return 'bg-green-100 text-green-800'
      case 'B+':
        return 'bg-blue-100 text-blue-800'
      case 'B':
        return 'bg-blue-100 text-blue-800'
      case 'C+':
        return 'bg-yellow-100 text-yellow-800'
      case 'C':
        return 'bg-yellow-100 text-yellow-800'
      case 'D':
        return 'bg-orange-100 text-orange-800'
      case 'F':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const reportStats = {
    total: reportCards.length,
    average: reportCards.length > 0 
      ? Math.round(reportCards.reduce((sum, card) => sum + card.percentage, 0) / reportCards.length)
      : 0,
    highest: Math.max(...reportCards.map(r => r.percentage)),
    lowest: Math.min(...reportCards.map(r => r.percentage)),
    published: reportCards.filter(r => r.status === 'PUBLISHED').length,
    generated: reportCards.filter(r => r.status === 'GENERATED').length
  }

  return (
    <DashboardLayout title="My Report Cards" navigation={studentNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Report Cards</h1>
            <p className="text-gray-600">View your academic performance reports</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download All
            </Button>
            <Button>
              <FileText className="w-4 h-4 mr-2" />
              Request Report
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportStats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{reportStats.average}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{reportStats.highest}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lowest Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{reportStats.lowest}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <FileText className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{reportStats.published}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Generated</CardTitle>
              <FileText className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{reportStats.generated}</div>
            </CardContent>
          </Card>
        </div>

        {/* Report Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reportCards.map((report) => (
            <Card key={report.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center">
                      <FileText className="w-5 h-5 mr-2 text-blue-600" />
                      {report.termName}
                    </CardTitle>
                    <CardDescription>{report.academicYear}</CardDescription>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                    {report.status}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Marks</p>
                      <p className="text-lg font-semibold">{report.totalMarks}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Obtained</p>
                      <p className="text-lg font-semibold">{report.obtainedMarks}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Percentage</p>
                      <p className="text-lg font-semibold text-blue-600">{report.percentage}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Grade</p>
                      <span className={`inline-flex px-2 py-1 text-sm font-semibold rounded-full ${getGradeColor(report.grade)}`}>
                        {report.grade}
                      </span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600">Class Rank</p>
                    <p className="text-lg font-semibold text-purple-600">#{report.rank}</p>
                  </div>

                  <div className="pt-4 border-t">
                    <p className="text-xs text-gray-500">
                      Generated: {new Date(report.generatedAt).toLocaleDateString()}
                    </p>
                  </div>

                  <div className="flex space-x-2 pt-4">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Printer className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Performance Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Summary</CardTitle>
            <CardDescription>
              Overview of your academic performance across all terms
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{reportStats.average}%</div>
                <div className="text-sm text-gray-600">Overall Average</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{reportStats.highest}%</div>
                <div className="text-sm text-gray-600">Best Performance</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{reportStats.total}</div>
                <div className="text-sm text-gray-600">Terms Completed</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
