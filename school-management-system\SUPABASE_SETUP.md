# Supabase Setup Guide

This guide will help you set up Supabase as your database for the School Management System.

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `school-management-system`
   - Database Password: Choose a strong password
   - Region: Choose closest to your users
5. Click "Create new project"

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **anon public** key (starts with `eyJ...`)

## Step 3: Update Environment Variables

1. Open your `env.local` file
2. Replace the placeholder values with your actual Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key-here
```

## Step 4: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase-schema.sql`
3. Paste it into the SQL editor
4. Click "Run" to execute the script

This will create all the necessary tables, indexes, and sample data.

## Step 5: Configure Authentication (Optional)

If you want to use Supabase Auth instead of NextAuth:

1. Go to **Authentication** → **Settings**
2. Configure your site URL: `http://localhost:3000`
3. Add redirect URLs if needed

## Step 6: Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Try to access your application
3. Check the browser console for any connection errors

## Database Structure

The Supabase setup includes the following main tables:

- **users** - User accounts and authentication
- **students** - Student profiles and information
- **teachers** - Teacher profiles and information
- **classes** - School classes/grades
- **sections** - Sections within classes
- **subjects** - Academic subjects
- **attendances** - Student attendance records
- **marks** - Student examination marks
- **terms** - Academic terms
- **exams** - Examinations
- **report_cards** - Generated report cards
- **audit_logs** - System audit trail
- **settings** - System configuration

## Row Level Security (RLS)

The database includes basic RLS policies for security:

- Users can only view their own data
- Students can only view their own student records
- Teachers can only view their own teacher records
- Authenticated users can view classes, sections, and subjects

You may need to customize these policies based on your specific requirements.

## Migration from Prisma

The new Supabase setup replaces the Prisma database. Key changes:

1. **Database Client**: Uses `@supabase/supabase-js` instead of `@prisma/client`
2. **Service Layer**: New service functions in `src/lib/db-supabase.ts`
3. **Types**: TypeScript types defined in `src/lib/supabase.ts`
4. **Schema**: SQL schema instead of Prisma schema

## Next Steps

After setting up Supabase:

1. Update your API routes to use the new Supabase services
2. Test all CRUD operations
3. Update any components that directly use Prisma
4. Remove Prisma dependencies if no longer needed

## Troubleshooting

### Connection Issues
- Verify your Supabase URL and anon key are correct
- Check that your project is not paused
- Ensure you're using the correct environment variables

### RLS Policy Issues
- Check the browser console for RLS errors
- Verify your authentication is working
- Adjust RLS policies in the Supabase dashboard

### Type Errors
- Make sure you're importing types from `src/lib/supabase.ts`
- Update any hardcoded types to match the new schema

## Support

If you encounter issues:
1. Check the Supabase documentation
2. Review the browser console for errors
3. Check the Supabase dashboard logs
4. Verify your environment variables are loaded correctly
