import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/rbac';
import TeacherForm from '@/components/teachers/teacher-form';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { adminNavigation } from '@/lib/navigation';

export default async function NewTeacherPage() {
  // Temporarily bypass authentication for testing
  // const session = await getServerSession(authOptions);
  
  // if (!session?.user) {
  //   redirect('/login');
  // }

  // if (!hasPermission(session.user.role as any, 'teachers:write')) {
  //   redirect('/unauthorized');
  // }

  return (
    <DashboardLayout 
      title="Add New Teacher"
      navigation={adminNavigation}
    >
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Add New Teacher</h1>
          <p className="text-gray-600 dark:text-gray-400">Create a new teacher account and assign login credentials.</p>
        </div>
        
        <TeacherForm mode="create" />
      </div>
    </DashboardLayout>
  );
}
