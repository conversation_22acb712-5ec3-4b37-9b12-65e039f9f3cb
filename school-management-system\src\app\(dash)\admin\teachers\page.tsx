'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import TeacherTable from '@/components/teachers/teacher-table';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { adminNavigation } from '@/lib/navigation';
import { 
  Users, 
  GraduationCap, 
  BookOpen, 
  FileText, 
  Calendar,
  BarChart3,
  Settings,
  UserPlus,
  ClipboardList,
  Award
} from 'lucide-react';

interface Teacher {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender?: string;
  qualification?: string;
  experience?: number;
  salary?: number;
  isActive: boolean;
  user: {
    id: number;
    email: string;
    role: string;
  };
  classes: Array<{
    id: number;
    name: string;
    section: {
      id: number;
      name: string;
    };
  }>;
  subjects: Array<{
    id: number;
    name: string;
  }>;
}

export default function TeachersPage() {
  const router = useRouter();
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState('all');

  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(filterActive !== 'all' && { isActive: filterActive }),
      });

      const response = await fetch(`/api/admin/teachers?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch teachers');
      }

      const data = await response.json();
      setTeachers(data.teachers);
      setPagination(data.pagination);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeachers();
  }, [pagination.page, searchTerm, filterActive]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleFilter = (filter: string) => {
    setFilterActive(filter);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  return (
    <DashboardLayout title="Teacher Management" navigation={adminNavigation}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Teacher Management</h1>
          <Button onClick={() => router.push('/admin/teachers/new')}>
            Add New Teacher
          </Button>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <TeacherTable
          teachers={teachers}
          pagination={pagination}
          onPageChange={handlePageChange}
          onSearch={handleSearch}
          onFilter={handleFilter}
          loading={loading}
        />

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Teachers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {teachers.filter(t => t.isActive).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Average Experience</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {teachers.length > 0 
                  ? Math.round(teachers.reduce((sum, t) => sum + (t.experience || 0), 0) / teachers.length)
                  : 0
                } years
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
