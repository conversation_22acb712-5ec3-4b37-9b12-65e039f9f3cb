@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-800;
  }
  body {
    @apply bg-white dark:bg-gray-950 text-gray-900 dark:text-gray-100;
  }
}

@layer utilities {
  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }
  
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }
  
  .text-responsive-base {
    @apply text-base sm:text-lg;
  }
  
  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }
  
  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }
  
  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl;
  }
  
  /* Mobile-first spacing utilities */
  .space-y-responsive {
    @apply space-y-4 sm:space-y-6;
  }
  
  .gap-responsive {
    @apply gap-4 sm:gap-6;
  }
  
  /* Responsive padding utilities */
  .p-responsive {
    @apply p-4 sm:p-6;
  }
  
  .px-responsive {
    @apply px-4 sm:px-6;
  }
  
  .py-responsive {
    @apply py-4 sm:py-6;
  }
  
  /* Mobile-friendly button sizing */
  .btn-mobile {
    @apply min-h-[44px] px-4 py-2;
  }
  
  /* Responsive container */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Mobile-friendly form inputs */
  .input-mobile {
    @apply min-h-[44px] text-base;
  }
  
  /* Responsive grid utilities */
  .grid-responsive-1 {
    @apply grid grid-cols-1;
  }
  
  .grid-responsive-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }
  
  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }
  
  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }
  
  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  /* Responsive flex utilities */
  .flex-responsive-col {
    @apply flex flex-col sm:flex-row;
  }
  
  .flex-responsive-row {
    @apply flex flex-row sm:flex-col;
  }
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Ensure tables don't break layout */
  .table-responsive {
    font-size: 14px;
  }
  
  /* Better mobile scrolling */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Mobile-friendly modals */
  .modal-mobile {
    margin: 0;
    max-height: 100vh;
    border-radius: 0;
  }
  
  /* Improve touch targets */
  button, 
  [role="button"], 
  input[type="submit"], 
  input[type="button"] {
    min-height: 44px;
  }
  
  /* Better mobile form styling */
  input, 
  select, 
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Tablet-specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet-specific styles */
  .tablet-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* High DPI display improvements */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Crisp borders and shadows on high DPI displays */
  .border {
    border-width: 0.5px;
  }
}
