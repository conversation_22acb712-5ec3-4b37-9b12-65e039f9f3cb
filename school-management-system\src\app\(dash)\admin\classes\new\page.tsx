import ClassForm from '@/components/classes/class-form';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { adminNavigation } from '@/lib/navigation';

export default function NewClassPage() {
  return (
    <DashboardLayout title="Add New Class" navigation={adminNavigation}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Add New Class</h1>
          <p className="text-gray-600 dark:text-gray-400">Create a new class and assign a teacher and section.</p>
        </div>
        
        <ClassForm mode="create" />
      </div>
    </DashboardLayout>
  );
}
