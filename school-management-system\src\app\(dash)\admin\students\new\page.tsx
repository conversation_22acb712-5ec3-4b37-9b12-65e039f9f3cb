import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { prisma as db } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import { StudentForm } from '@/components/students/student-form';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { adminNavigation } from '@/lib/navigation';

export default async function NewStudentPage() {
  // Temporarily bypass authentication for testing
  // const session = await getServerSession(authOptions);
  
  // if (!session?.user) {
  //   redirect('/login');
  // }

  // if (!hasPermission(session.user.role as any, 'students:write')) {
  //   redirect('/unauthorized');
  // }

  // Fetch classes for the form
  const classes = await db.class.findMany({
    include: {
      sections: true,
    },
    orderBy: [
      { name: 'asc' },
    ],
  });

  return (
    <DashboardLayout 
      title="Add New Student"
      navigation={adminNavigation}
    >
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Student</h1>
          <p className="text-muted-foreground">
            Create a new student account and add them to the system
          </p>
        </div>
        
        <StudentForm 
          classes={classes} 
          mode="create" 
        />
      </div>
    </DashboardLayout>
  );
}
