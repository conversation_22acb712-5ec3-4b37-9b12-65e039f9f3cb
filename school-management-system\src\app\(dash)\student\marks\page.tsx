'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { studentNavigation } from '@/lib/navigation'
import {
  Calendar,
  Award,
  BookOpen,
  TrendingUp,
  FileText,
  Download,
  Eye
} from 'lucide-react'

interface MarkRecord {
  id: string
  obtainedMarks: number
  remarks?: string
  createdAt: string
  updatedAt: string
  exam: {
    id: string
    name: string
    maxMarks: number
    date: string
    subject: {
      id: string
      name: string
      code: string
    }
    term: {
      id: string
      name: string
    }
  }
}



export default function StudentMarksPage() {
  const { data: session } = useSession()
  const [markRecords, setMarkRecords] = useState<MarkRecord[]>([])
  const [selectedTerm, setSelectedTerm] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')
  const [loading, setLoading] = useState(true)
  const [terms, setTerms] = useState<Array<{id: string, name: string}>>([])
  const [subjects, setSubjects] = useState<Array<{id: string, name: string}>>([])

  useEffect(() => {
    fetchMarks()
    fetchTermsAndSubjects()
  }, [selectedTerm, selectedSubject])

  const fetchMarks = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedTerm !== 'all') params.append('termId', selectedTerm)
      if (selectedSubject !== 'all') params.append('subjectId', selectedSubject)

      const response = await fetch(`/api/student/marks?${params}`)
      if (response.ok) {
        const data = await response.json()
        setMarkRecords(data)
      } else {
        console.error('Failed to fetch marks')
      }
    } catch (error) {
      console.error('Error fetching marks:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTermsAndSubjects = async () => {
    try {
      // Fetch terms and subjects for filters
      // For now, we'll use static data but this could be from API
      setTerms([
        { id: 'term1', name: 'Term 1' },
        { id: 'term2', name: 'Term 2' },
        { id: 'term3', name: 'Term 3' }
      ])
      setSubjects([
        { id: 'math', name: 'Mathematics' },
        { id: 'english', name: 'English' },
        { id: 'science', name: 'Science' },
        { id: 'social', name: 'Social Studies' },
        { id: 'computer', name: 'Computer Science' }
      ])
    } catch (error) {
      console.error('Error fetching terms and subjects:', error)
    }
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
        return 'bg-green-100 text-green-800'
      case 'A':
        return 'bg-green-100 text-green-800'
      case 'B+':
        return 'bg-blue-100 text-blue-800'
      case 'B':
        return 'bg-blue-100 text-blue-800'
      case 'C+':
        return 'bg-yellow-100 text-yellow-800'
      case 'C':
        return 'bg-yellow-100 text-yellow-800'
      case 'D':
        return 'bg-orange-100 text-orange-800'
      case 'F':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getGrade = (percentage: number) => {
    if (percentage >= 90) return 'A+'
    if (percentage >= 80) return 'A'
    if (percentage >= 70) return 'B+'
    if (percentage >= 60) return 'B'
    if (percentage >= 50) return 'C+'
    if (percentage >= 40) return 'C'
    if (percentage >= 30) return 'D'
    return 'F'
  }

  const marksWithCalculations = markRecords.map(record => {
    const percentage = Math.round((record.obtainedMarks / record.exam.maxMarks) * 100 * 100) / 100
    const grade = getGrade(percentage)
    return {
      ...record,
      percentage,
      grade
    }
  })

  const marksStats = {
    total: marksWithCalculations.length,
    average: marksWithCalculations.length > 0
      ? Math.round(marksWithCalculations.reduce((sum, record) => sum + record.percentage, 0) / marksWithCalculations.length)
      : 0,
    highest: marksWithCalculations.length > 0 ? Math.max(...marksWithCalculations.map(r => r.percentage)) : 0,
    lowest: marksWithCalculations.length > 0 ? Math.min(...marksWithCalculations.map(r => r.percentage)) : 0,
    passed: marksWithCalculations.filter(r => r.percentage >= 40).length,
    failed: marksWithCalculations.filter(r => r.percentage < 40).length
  }

  return (
    <DashboardLayout title="My Marks" navigation={studentNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">My Marks</h1>
            <p className="text-sm sm:text-base text-gray-600">View your examination marks and grades</p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
            <Button variant="outline" className="w-full sm:w-auto">
              <Download className="w-4 h-4 mr-2" />
              <span className="sm:hidden">Download</span>
              <span className="hidden sm:inline">Download Report</span>
            </Button>
            <Button className="w-full sm:w-auto">
              <FileText className="w-4 h-4 mr-2" />
              <span className="sm:hidden">Report Card</span>
              <span className="hidden sm:inline">View Report Card</span>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{marksStats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{marksStats.average}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.highest}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lowest Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{marksStats.lowest}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Passed</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.passed}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <Award className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{marksStats.failed}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="term" className="block text-sm font-medium text-gray-700 mb-2">
                  Term
                </label>
                <select
                  id="term"
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Terms</option>
                  {terms.map(term => (
                    <option key={term.id} value={term.id}>{term.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  Subject
                </label>
                <select
                  id="subject"
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Subjects</option>
                  {subjects.map(subject => (
                    <option key={subject.id} value={subject.id}>{subject.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Marks Table */}
        <Card>
          <CardHeader>
            <CardTitle>My Examination Marks</CardTitle>
            <CardDescription>
              Detailed view of all your examination results
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading marks...</p>
              </div>
            ) : marksWithCalculations.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No marks found</p>
              </div>
            ) : (
              <>
                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Exam
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Subject
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Term
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Marks
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Percentage
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Grade
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {marksWithCalculations.map((record) => (
                        <tr key={record.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <BookOpen className="w-4 h-4 mr-2 text-blue-600" />
                              <span className="text-sm font-medium text-gray-900">{record.exam.name}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {record.exam.subject.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {record.exam.term.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {record.obtainedMarks}/{record.exam.maxMarks}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {record.percentage}%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(record.grade)}`}>
                              {record.grade}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(record.exam.date).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Mobile Cards */}
                <div className="lg:hidden space-y-4">
                  {marksWithCalculations.map((record) => (
                    <Card key={record.id} className="p-4">
                      <div className="flex flex-col space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <BookOpen className="w-5 h-5 text-blue-600 flex-shrink-0" />
                            <div className="min-w-0 flex-1">
                              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                                {record.exam.name}
                              </h3>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {record.exam.subject.name} • {record.exam.term.name}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(record.grade)}`}>
                              {record.grade}
                            </span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700 dark:text-gray-300">Marks:</span>
                            <p className="text-gray-600 dark:text-gray-400">{record.obtainedMarks}/{record.exam.maxMarks}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700 dark:text-gray-300">Percentage:</span>
                            <p className="text-gray-600 dark:text-gray-400 font-semibold">{record.percentage}%</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700 dark:text-gray-300">Date:</span>
                            <p className="text-gray-600 dark:text-gray-400">{new Date(record.exam.date).toLocaleDateString()}</p>
                          </div>
                          <div className="flex justify-end">
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4 mr-1" />
                              View Details
                            </Button>
                          </div>
                          {record.remarks && (
                            <div className="col-span-2">
                              <span className="font-medium text-gray-700 dark:text-gray-300">Remarks:</span>
                              <p className="text-gray-600 dark:text-gray-400">{record.remarks}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
