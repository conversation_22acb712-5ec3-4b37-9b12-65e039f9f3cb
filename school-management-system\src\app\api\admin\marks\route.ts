import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const examId = searchParams.get('examId')
    const subjectId = searchParams.get('subjectId')
    const classId = searchParams.get('classId')

    const where: any = {}
    
    if (examId && examId !== 'all') {
      where.examId = examId
    }
    if (subjectId && subjectId !== 'all') {
      where.exam = {
        subjectId: subjectId
      }
    }
    if (classId && classId !== 'all') {
      where.student = {
        currentClassId: classId
      }
    }

    const marks = await prisma.mark.findMany({
      where,
      include: {
        student: {
          include: {
            user: true,
            currentClass: true,
            currentSection: true
          }
        },
        exam: {
          include: {
            subject: true,
            term: true
          }
        },
        gradedByTeacher: {
          include: {
            user: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(marks)
  } catch (error) {
    console.error('Error fetching marks:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { studentId, examId, obtainedMarks, remarks, gradedByTeacherId } = body

    const mark = await prisma.mark.create({
      data: {
        studentId,
        examId,
        obtainedMarks,
        remarks,
        gradedByTeacherId
      }
    })

    return NextResponse.json(mark)
  } catch (error) {
    console.error('Error creating mark:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
