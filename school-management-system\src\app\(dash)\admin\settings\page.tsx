'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { 
  Calendar, 
  Settings, 
  School, 
  Users, 
  Bell,
  Shield,
  Database,
  Mail,
  Save,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface SchoolInfo {
  name: string
  address: string
  phone: string
  email: string
  website: string
  principal: string
  establishedYear: string
}

interface AcademicSettings {
  academicYear: string
  currentTerm: string
  gradingSystem: 'LETTER' | 'PERCENTAGE' | 'NUMERIC'
  passPercentage: number
  maxAttendancePercentage: number
}

interface NotificationSettings {
  attendanceAlerts: boolean
  examResults: boolean
  reportCardGeneration: boolean
  systemUpdates: boolean
}

interface SecuritySettings {
  sessionTimeout: number
  passwordPolicy: string
  twoFactorAuth: boolean
  loginAttempts: boolean
}

import { adminNavigation } from '@/lib/navigation';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'general' | 'academic' | 'notifications' | 'security'>('general')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  
  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo>({
    name: 'Advance School',
    address: '123 Education Street, City, State 12345',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'www.advanceschool.edu',
    principal: 'Dr. John Smith',
    establishedYear: '1995'
  })
  const [academicSettings, setAcademicSettings] = useState<AcademicSettings>({
    academicYear: '2024-2025',
    currentTerm: 'Term 1',
    gradingSystem: 'LETTER',
    passPercentage: 40,
    maxAttendancePercentage: 75
  })
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    attendanceAlerts: true,
    examResults: true,
    reportCardGeneration: false,
    systemUpdates: true
  })
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    sessionTimeout: 30,
    passwordPolicy: 'strong',
    twoFactorAuth: false,
    loginAttempts: true
  })

  // Load settings from API
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/admin/settings')
        if (response.ok) {
          const data = await response.json()
          if (data.general) {
            setSchoolInfo({
              name: data.general.schoolName,
              address: data.general.address,
              phone: data.general.phone,
              email: data.general.email,
              website: data.general.website,
              principal: data.general.principal,
              establishedYear: data.general.establishedYear
            })
          }
          if (data.academic) {
            setAcademicSettings({
              academicYear: data.academic.academicYear,
              currentTerm: data.academic.currentTerm,
              gradingSystem: data.academic.gradingSystem,
              passPercentage: data.academic.passPercentage,
              maxAttendancePercentage: data.academic.maxAttendancePercentage
            })
          }
          if (data.notifications) {
            setNotificationSettings({
              attendanceAlerts: data.notifications.attendanceAlerts,
              examResults: data.notifications.examResults,
              reportCardGeneration: data.notifications.reportCardGeneration,
              systemUpdates: data.notifications.systemUpdates
            })
          }
          if (data.security) {
            setSecuritySettings({
              sessionTimeout: data.security.sessionTimeout,
              passwordPolicy: data.security.passwordPolicy,
              twoFactorAuth: data.security.twoFactorAuth,
              loginAttempts: data.security.loginAttempts
            })
          }
        }
      } catch (error) {
        console.error('Error loading settings:', error)
      }
    }

    loadSettings()
  }, [])

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 3000)
  }

  const handleSaveSchoolInfo = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'general', data: schoolInfo })
      })
      
      if (response.ok) {
        showMessage('success', 'School information saved successfully!')
      } else {
        showMessage('error', 'Failed to save school information')
      }
    } catch (error) {
      showMessage('error', 'Error saving school information')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveAcademicSettings = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'academic', data: academicSettings })
      })
      
      if (response.ok) {
        showMessage('success', 'Academic settings saved successfully!')
      } else {
        showMessage('error', 'Failed to save academic settings')
      }
    } catch (error) {
      showMessage('error', 'Error saving academic settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveNotificationSettings = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'notifications', data: notificationSettings })
      })
      
      if (response.ok) {
        showMessage('success', 'Notification settings saved successfully!')
      } else {
        showMessage('error', 'Failed to save notification settings')
      }
    } catch (error) {
      showMessage('error', 'Error saving notification settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSecuritySettings = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'security', data: securitySettings })
      })
      
      if (response.ok) {
        showMessage('success', 'Security settings saved successfully!')
      } else {
        showMessage('error', 'Failed to save security settings')
      }
    } catch (error) {
      showMessage('error', 'Error saving security settings')
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout title="System Settings" navigation={adminNavigation}>
      <div className="space-y-6">
        {/* Message Display */}
        {message && (
          <div className={`p-4 rounded-md flex items-center ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800' 
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 mr-2" />
            ) : (
              <AlertCircle className="w-5 h-5 mr-2" />
            )}
            {message.text}
          </div>
        )}

        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">System Settings</h1>
            <p className="text-sm sm:text-base text-gray-600">Manage school configuration and preferences</p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
            <Button variant="outline" disabled={loading} className="w-full sm:w-auto">
              <RefreshCw className="w-4 h-4 mr-2" />
              <span className="sm:hidden">Reset</span>
              <span className="hidden sm:inline">Reset to Default</span>
            </Button>
            <Button disabled={loading} className="w-full sm:w-auto">
              <Save className="w-4 h-4 mr-2" />
              <span className="sm:hidden">Save All</span>
              <span className="hidden sm:inline">Save All Changes</span>
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex flex-wrap gap-2 sm:gap-8">
            <button
              onClick={() => setActiveTab('general')}
              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${
                activeTab === 'general'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <School className="inline w-4 h-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">General Settings</span>
              <span className="sm:hidden">General</span>
            </button>
            <button
              onClick={() => setActiveTab('academic')}
              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${
                activeTab === 'academic'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Calendar className="inline w-4 h-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Academic Settings</span>
              <span className="sm:hidden">Academic</span>
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${
                activeTab === 'notifications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Bell className="inline w-4 h-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Notifications</span>
              <span className="sm:hidden">Alerts</span>
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${
                activeTab === 'security'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Shield className="inline w-4 h-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Security</span>
              <span className="sm:hidden">Security</span>
            </button>
          </nav>
        </div>

        {/* General Settings Tab */}
        {activeTab === 'general' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <School className="w-5 h-5 mr-2" />
                  School Information
                </CardTitle>
                <CardDescription>
                  Update basic school information and contact details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="schoolName">School Name</Label>
                    <Input
                      id="schoolName"
                      value={schoolInfo.name}
                      onChange={(e) => setSchoolInfo({...schoolInfo, name: e.target.value})}
                      placeholder="Enter school name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="principal">Principal Name</Label>
                    <Input
                      id="principal"
                      value={schoolInfo.principal}
                      onChange={(e) => setSchoolInfo({...schoolInfo, principal: e.target.value})}
                      placeholder="Enter principal name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={schoolInfo.phone}
                      onChange={(e) => setSchoolInfo({...schoolInfo, phone: e.target.value})}
                      placeholder="Enter phone number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={schoolInfo.email}
                      onChange={(e) => setSchoolInfo({...schoolInfo, email: e.target.value})}
                      placeholder="Enter email address"
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={schoolInfo.website}
                      onChange={(e) => setSchoolInfo({...schoolInfo, website: e.target.value})}
                      placeholder="Enter website URL"
                    />
                  </div>
                  <div>
                    <Label htmlFor="establishedYear">Established Year</Label>
                    <Input
                      id="establishedYear"
                      value={schoolInfo.establishedYear}
                      onChange={(e) => setSchoolInfo({...schoolInfo, establishedYear: e.target.value})}
                      placeholder="Enter established year"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={schoolInfo.address}
                      onChange={(e) => setSchoolInfo({...schoolInfo, address: e.target.value})}
                      placeholder="Enter complete address"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <Button onClick={handleSaveSchoolInfo} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    {loading ? 'Saving...' : 'Save School Information'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Academic Settings Tab */}
        {activeTab === 'academic' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Academic Configuration
                </CardTitle>
                <CardDescription>
                  Configure academic year, grading system, and performance criteria
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="academicYear">Academic Year</Label>
                    <Input
                      id="academicYear"
                      value={academicSettings.academicYear}
                      onChange={(e) => setAcademicSettings({...academicSettings, academicYear: e.target.value})}
                      placeholder="e.g., 2024-2025"
                    />
                  </div>
                  <div>
                    <Label htmlFor="currentTerm">Current Term</Label>
                    <select
                      id="currentTerm"
                      value={academicSettings.currentTerm}
                      onChange={(e) => setAcademicSettings({...academicSettings, currentTerm: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Term 1">Term 1</option>
                      <option value="Term 2">Term 2</option>
                      <option value="Term 3">Term 3</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="gradingSystem">Grading System</Label>
                    <select
                      id="gradingSystem"
                      value={academicSettings.gradingSystem}
                      onChange={(e) => setAcademicSettings({...academicSettings, gradingSystem: e.target.value as any})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="LETTER">Letter Grades (A+, A, B+, B, etc.)</option>
                      <option value="PERCENTAGE">Percentage</option>
                      <option value="NUMERIC">Numeric (1-10)</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="passPercentage">Pass Percentage</Label>
                    <Input
                      id="passPercentage"
                      type="number"
                      value={academicSettings.passPercentage}
                      onChange={(e) => setAcademicSettings({...academicSettings, passPercentage: parseInt(e.target.value)})}
                      placeholder="40"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxAttendancePercentage">Minimum Attendance %</Label>
                    <Input
                      id="maxAttendancePercentage"
                      type="number"
                      value={academicSettings.maxAttendancePercentage}
                      onChange={(e) => setAcademicSettings({...academicSettings, maxAttendancePercentage: parseInt(e.target.value)})}
                      placeholder="75"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <Button onClick={handleSaveAcademicSettings} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    {loading ? 'Saving...' : 'Save Academic Settings'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Notifications Tab */}
        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="w-5 h-5 mr-2" />
                  Notification Settings
                </CardTitle>
                <CardDescription>
                  Configure email notifications and alerts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Attendance Alerts</h4>
                      <p className="text-sm text-gray-600">Send notifications for low attendance</p>
                    </div>
                    <input 
                      type="checkbox" 
                      className="rounded" 
                      checked={notificationSettings.attendanceAlerts}
                      onChange={(e) => setNotificationSettings({
                        ...notificationSettings,
                        attendanceAlerts: e.target.checked
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Exam Results</h4>
                      <p className="text-sm text-gray-600">Notify when exam results are published</p>
                    </div>
                    <input 
                      type="checkbox" 
                      className="rounded" 
                      checked={notificationSettings.examResults}
                      onChange={(e) => setNotificationSettings({
                        ...notificationSettings,
                        examResults: e.target.checked
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Report Card Generation</h4>
                      <p className="text-sm text-gray-600">Notify when report cards are ready</p>
                    </div>
                    <input 
                      type="checkbox" 
                      className="rounded" 
                      checked={notificationSettings.reportCardGeneration}
                      onChange={(e) => setNotificationSettings({
                        ...notificationSettings,
                        reportCardGeneration: e.target.checked
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">System Updates</h4>
                      <p className="text-sm text-gray-600">Receive system maintenance notifications</p>
                    </div>
                    <input 
                      type="checkbox" 
                      className="rounded" 
                      checked={notificationSettings.systemUpdates}
                      onChange={(e) => setNotificationSettings({
                        ...notificationSettings,
                        systemUpdates: e.target.checked
                      })}
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <Button onClick={handleSaveNotificationSettings} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    {loading ? 'Saving...' : 'Save Notification Settings'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="w-5 h-5 mr-2" />
                  Security Settings
                </CardTitle>
                <CardDescription>
                  Manage password policies and security settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        sessionTimeout: parseInt(e.target.value)
                      })}
                      placeholder="30"
                    />
                  </div>
                  <div>
                    <Label htmlFor="passwordPolicy">Password Policy</Label>
                    <select
                      id="passwordPolicy"
                      value={securitySettings.passwordPolicy}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        passwordPolicy: e.target.value
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="strong">Strong (8+ chars, mixed case, numbers)</option>
                      <option value="medium">Medium (6+ chars, mixed case)</option>
                      <option value="weak">Weak (4+ chars)</option>
                    </select>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Two-Factor Authentication</h4>
                      <p className="text-sm text-gray-600">Require 2FA for admin accounts</p>
                    </div>
                    <input 
                      type="checkbox" 
                      className="rounded" 
                      checked={securitySettings.twoFactorAuth}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        twoFactorAuth: e.target.checked
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Login Attempts</h4>
                      <p className="text-sm text-gray-600">Lock account after failed attempts</p>
                    </div>
                    <input 
                      type="checkbox" 
                      className="rounded" 
                      checked={securitySettings.loginAttempts}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        loginAttempts: e.target.checked
                      })}
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <Button onClick={handleSaveSecuritySettings} disabled={loading}>
                    <Save className="w-4 h-4 mr-2" />
                    {loading ? 'Saving...' : 'Save Security Settings'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="w-5 h-5 mr-2" />
                  System Maintenance
                </CardTitle>
                <CardDescription>
                  Database backup and system maintenance options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button variant="outline" className="w-full">
                    <Database className="w-4 h-4 mr-2" />
                    Create Database Backup
                  </Button>
                  <Button variant="outline" className="w-full">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Clear Cache
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Mail className="w-4 h-4 mr-2" />
                    Test Email Configuration
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
