'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { LogOut, Loader2 } from 'lucide-react'
import { logoutUser } from '@/lib/auth-utils'

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
  showIcon?: boolean
  redirectUrl?: string
  children?: React.ReactNode
  onClick?: () => void
}

export function LogoutButton({
  variant = 'ghost',
  size = 'default',
  className,
  showIcon = true,
  redirectUrl = '/login',
  children,
  onClick
}: LogoutButtonProps) {
  const [loading, setLoading] = useState(false)

  const handleLogout = async () => {
    setLoading(true)
    try {
      if (onClick) {
        onClick()
      }
      await logoutUser(redirectUrl)
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      disabled={loading}
      onClick={handleLogout}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : showIcon ? (
        <LogOut className="h-4 w-4 mr-2" />
      ) : null}
      {children || 'Sign Out'}
    </Button>
  )
}

/**
 * Simple logout button without confirmation dialog
 */
export function SimpleLogoutButton({
  variant = 'ghost',
  size = 'default',
  className,
  redirectUrl = '/login'
}: Omit<LogoutButtonProps, 'children' | 'onClick'>) {
  return (
    <LogoutButton
      variant={variant}
      size={size}
      className={className}
      redirectUrl={redirectUrl}
    />
  )
}

/**
 * Logout menu item for dropdown menus
 */
export function LogoutMenuItem({
  className,
  redirectUrl = '/login'
}: {
  className?: string
  redirectUrl?: string
}) {
  const [loading, setLoading] = useState(false)

  const handleLogout = async () => {
    setLoading(true)
    try {
      await logoutUser(redirectUrl)
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <button
      onClick={handleLogout}
      disabled={loading}
      className={`flex w-full items-center px-2 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 disabled:opacity-50 ${className}`}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <LogOut className="h-4 w-4 mr-2" />
      )}
      Sign Out
    </button>
  )
}
