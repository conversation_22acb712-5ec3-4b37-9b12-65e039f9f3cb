import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/rbac';
import { BulkImport } from '@/components/students/bulk-import';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Users, Upload } from 'lucide-react';

export default async function BulkImportPage() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    redirect('/login');
  }

  if (!hasPermission(session.user.role as any, 'students:write')) {
    redirect('/unauthorized');
  }

  return (
    <DashboardLayout 
      title="Bulk Import Students"
      navigation={[
        { name: 'Students', href: '/admin/students', icon: 'Users' },
        { name: 'Bulk Import', href: '#', icon: 'Upload' },
      ]}
    >
      <BulkImport />
    </DashboardLayout>
  );
}
