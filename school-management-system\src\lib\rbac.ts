import { UserRole } from '@prisma/client'

// Define permission types
export type Permission = 
  | 'users:read' | 'users:write' | 'users:delete'
  | 'students:read' | 'students:write' | 'students:delete'
  | 'teachers:read' | 'teachers:write' | 'teachers:delete'
  | 'classes:read' | 'classes:write' | 'classes:delete'
  | 'subjects:read' | 'subjects:write' | 'subjects:delete'
  | 'attendance:read' | 'attendance:write'
  | 'marks:read' | 'marks:write'
  | 'reports:read' | 'reports:write'
  | 'settings:read' | 'settings:write'
  | 'audit:read'

// Define role permissions
const rolePermissions: Record<UserRole, Permission[]> = {
  ADMIN: [
    'users:read', 'users:write', 'users:delete',
    'students:read', 'students:write', 'students:delete',
    'teachers:read', 'teachers:write', 'teachers:delete',
    'classes:read', 'classes:write', 'classes:delete',
    'subjects:read', 'subjects:write', 'subjects:delete',
    'attendance:read', 'attendance:write',
    'marks:read', 'marks:write',
    'reports:read', 'reports:write',
    'settings:read', 'settings:write',
    'audit:read'
  ],
  TEACHER: [
    'students:read',
    'attendance:read', 'attendance:write',
    'marks:read', 'marks:write',
    'reports:read'
  ],
  STUDENT: [
    'attendance:read',
    'marks:read',
    'reports:read'
  ]
}

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userRole: UserRole | string, permission: Permission): boolean {
  const role = userRole as UserRole;
  return rolePermissions[role]?.includes(permission) ?? false
}

/**
 * Check if a user can access a specific resource
 */
export function canAccess(userRole: UserRole | string, resource: string, action: 'read' | 'write' | 'delete'): boolean {
  const permission = `${resource}:${action}` as Permission
  return hasPermission(userRole, permission)
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: UserRole | string): Permission[] {
  const userRole = role as UserRole;
  return rolePermissions[userRole] ?? []
}

/**
 * Check if user can access student data (teachers can only see their assigned students)
 */
export function canAccessStudentData(userRole: UserRole | string, teacherId?: string, studentClassId?: string): boolean {
  const role = userRole as UserRole;
  if (role === 'ADMIN') return true
  if (role === 'STUDENT') return false // Students can't access other students' data
  
  // For teachers, we'll need additional logic to check if they're assigned to the student's class
  // This will be implemented when we add teacher-class assignments
  return role === 'TEACHER'
}

/**
 * Check if user can access class data (teachers can only see their assigned classes)
 */
export function canAccessClassData(userRole: UserRole | string, teacherId?: string, classId?: string): boolean {
  const role = userRole as UserRole;
  if (role === 'ADMIN') return true
  
  // For teachers, we'll need additional logic to check if they're assigned to the class
  // This will be implemented when we add teacher-class assignments
  return role === 'TEACHER'
}
