import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';
import { hasPermission } from '@/lib/rbac';

// Validation schema for updating a student
const updateStudentSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  dateOfBirth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  phoneNumber: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  admissionDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
  classId: z.string().optional(),
  parentName: z.string().optional(),
  parentPhone: z.string().optional(),
  parentEmail: z.string().email('Invalid parent email').optional(),
});

// GET /api/admin/students/[id] - Get student details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Temporarily bypass authentication for testing
    // const session = await getServerSession(authOptions);
    // if (!session?.user) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    // if (!hasPermission(session.user.role, 'students:read')) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    // }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Fetch student with all related data
    const student = await prisma.student.findUnique({
      where: { id: parseInt(id) },
      include: {
        user: true,
        currentClass: true,
        currentSection: true,
      },
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ student });
  } catch (error) {
    console.error('Error fetching student:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/students/[id] - Update student
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Temporarily bypass authentication for testing
    // const session = await getServerSession(authOptions);
    // if (!session?.user) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    // if (!hasPermission(session.user.role, 'students:write')) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    // }

    const resolvedParams = await params;
    const { id } = resolvedParams;
    const body = await request.json();
    const validatedData = updateStudentSchema.parse(body);

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id: parseInt(id) },
      include: { user: true },
    });

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    // Check if email is being changed and if it already exists
    if (validatedData.email && validatedData.email !== existingStudent.user?.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: validatedData.email }
      });

      if (emailExists) {
        return NextResponse.json(
          { error: 'A user with this email already exists' },
          { status: 400 }
        );
      }
    }

    // Parse class and section from classId (format: "classId-sectionId")
    let classId = null;
    let sectionId = null;
    
    if (validatedData.classId) {
      [classId, sectionId] = validatedData.classId.split('-');
      
      // Check if class exists
      const classExists = await prisma.class.findUnique({
        where: { id: parseInt(classId) }
      });

      if (!classExists) {
        return NextResponse.json(
          { error: 'Class not found' },
          { status: 400 }
        );
      }

      // Check if section exists
      if (sectionId) {
        const sectionExists = await prisma.section.findUnique({
          where: { id: parseInt(sectionId) }
        });

        if (!sectionExists) {
          return NextResponse.json(
            { error: 'Section not found' },
            { status: 400 }
          );
        }
      }
    }

    // Prepare update data for user
    const userUpdateData: any = {};
    if (validatedData.firstName) userUpdateData.firstName = validatedData.firstName;
    if (validatedData.lastName) userUpdateData.lastName = validatedData.lastName;
    if (validatedData.email) userUpdateData.email = validatedData.email;
    if (validatedData.phoneNumber) userUpdateData.phone = validatedData.phoneNumber;

    // Prepare update data for student
    const studentUpdateData: any = {};
    if (validatedData.dateOfBirth) studentUpdateData.dateOfBirth = new Date(validatedData.dateOfBirth);
    if (validatedData.gender) studentUpdateData.gender = validatedData.gender;
    if (validatedData.address) studentUpdateData.address = validatedData.address;
    if (validatedData.emergencyContact) studentUpdateData.guardianName = validatedData.emergencyContact;
    if (validatedData.emergencyPhone) studentUpdateData.guardianPhone = validatedData.emergencyPhone;
    if (classId) studentUpdateData.currentClassId = parseInt(classId);
    if (sectionId) studentUpdateData.currentSectionId = parseInt(sectionId);

    // Update user and student
    const updatedUser = await prisma.user.update({
      where: { id: existingStudent.userId },
      data: userUpdateData,
    });
    const updatedStudent = await prisma.student.update({
      where: { id: parseInt(id) },
      data: studentUpdateData,
      include: {
        user: true,
        currentClass: true,
        currentSection: true,
      },
    });

    return NextResponse.json({
      message: 'Student updated successfully',
      student: updatedStudent,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }

    console.error('Error updating student:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/students/[id] - Delete student
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Temporarily bypass authentication for testing
    // const session = await getServerSession(authOptions);
    // if (!session?.user) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    // if (!hasPermission(session.user.role, 'students:delete')) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    // }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: parseInt(id) },
      include: { user: true },
    });

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    // Delete student and associated user account
    await prisma.student.delete({
      where: { id: parseInt(id) },
    });
    await prisma.user.delete({
      where: { id: student.userId },
    });

    return NextResponse.json({
      message: 'Student deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting student:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
