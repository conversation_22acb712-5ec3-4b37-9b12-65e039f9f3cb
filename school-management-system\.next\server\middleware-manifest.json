{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/school-management-system_8c1ea602._.js", "server/edge/chunks/[root-of-the-server]__433d4839._.js", "server/edge/chunks/turbopack-school-management-system_edge-wrapper_db311ba0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/teacher(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/teacher/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/student(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/student/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/teacher(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/teacher/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/student(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/student/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "O72Sp333G9CD2xefaiYCeef/o971MfYvqNDNBykp5go=", "__NEXT_PREVIEW_MODE_ID": "a22af09566be91fa22c803cbfb281145", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "752e544f64b66077793e652e98d4543fd20878830a9b17439c24f05d42e9232e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c83cc79c025e0dd298e83cbeef4e1bdd5a0c4e2947f52872c789988e9956a5b4"}}}, "sortedMiddleware": ["/"], "functions": {}}