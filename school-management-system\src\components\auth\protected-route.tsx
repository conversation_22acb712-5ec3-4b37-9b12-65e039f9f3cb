'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Loader2 } from 'lucide-react'
import { checkAuth } from '@/lib/auth-utils'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'ADMIN' | 'TEACHER' | 'STUDENT'
  fallbackUrl?: string
  loadingComponent?: React.ReactNode
}

export function ProtectedRoute({
  children,
  requiredRole,
  fallbackUrl = '/login',
  loadingComponent
}: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null)

  useEffect(() => {
    const checkAuthorization = async () => {
      if (status === 'loading') {
        return // Still loading session
      }

      if (status === 'unauthenticated') {
        router.push(fallbackUrl)
        return
      }

      if (status === 'authenticated') {
        // Check if user has required role
        if (requiredRole && session?.user?.role !== requiredRole) {
          router.push('/unauthorized')
          return
        }

        setIsAuthorized(true)
      }
    }

    checkAuthorization()
  }, [session, status, requiredRole, router, fallbackUrl])

  // Show loading while checking authentication
  if (status === 'loading' || isAuthorized === null) {
    return loadingComponent || <DefaultLoadingComponent />
  }

  // Show nothing while redirecting
  if (!isAuthorized) {
    return null
  }

  return <>{children}</>
}

/**
 * Default loading component
 */
function DefaultLoadingComponent() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
        <p className="text-gray-600 dark:text-gray-400">Loading...</p>
      </div>
    </div>
  )
}

/**
 * Higher-order component for protecting pages
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: 'ADMIN' | 'TEACHER' | 'STUDENT'
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requiredRole={requiredRole}>
        <Component {...props} />
      </ProtectedRoute>
    )
  }
}

/**
 * Hook for checking authentication status
 */
export function useAuth(requiredRole?: 'ADMIN' | 'TEACHER' | 'STUDENT') {
  const { data: session, status } = useSession()
  const [authState, setAuthState] = useState({
    isLoading: true,
    isAuthenticated: false,
    hasRequiredRole: false,
    user: null as any
  })

  useEffect(() => {
    const checkAuthState = async () => {
      if (status === 'loading') {
        setAuthState(prev => ({ ...prev, isLoading: true }))
        return
      }

      if (status === 'unauthenticated') {
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          hasRequiredRole: false,
          user: null
        })
        return
      }

      if (status === 'authenticated' && session?.user) {
        const hasRequiredRole = !requiredRole || session.user.role === requiredRole

        setAuthState({
          isLoading: false,
          isAuthenticated: true,
          hasRequiredRole,
          user: session.user
        })
      }
    }

    checkAuthState()
  }, [session, status, requiredRole])

  return authState
}

/**
 * Component for role-based content rendering
 */
interface RoleBasedProps {
  allowedRoles: ('ADMIN' | 'TEACHER' | 'STUDENT')[]
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function RoleBased({ allowedRoles, children, fallback = null }: RoleBasedProps) {
  const { data: session } = useSession()

  if (!session?.user?.role || !allowedRoles.includes(session.user.role as any)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * Admin-only content wrapper
 */
export function AdminOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBased allowedRoles={['ADMIN']} fallback={fallback}>
      {children}
    </RoleBased>
  )
}

/**
 * Teacher-only content wrapper (includes admin access)
 */
export function TeacherOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBased allowedRoles={['ADMIN', 'TEACHER']} fallback={fallback}>
      {children}
    </RoleBased>
  )
}

/**
 * Student-only content wrapper (includes admin access)
 */
export function StudentOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleBased allowedRoles={['ADMIN', 'STUDENT']} fallback={fallback}>
      {children}
    </RoleBased>
  )
}
