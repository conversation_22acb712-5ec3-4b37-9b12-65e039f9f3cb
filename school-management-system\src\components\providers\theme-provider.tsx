'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'

type Theme = 'light' | 'dark' | 'system'

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  actualTheme: 'light' | 'dark'
  mounted: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    // Return default values instead of throwing error during SSR
    return {
      theme: 'system' as Theme,
      setTheme: () => {},
      actualTheme: 'light' as 'light' | 'dark',
      mounted: false
    }
  }
  return context
}

interface ThemeProviderProps {
  children: ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>('system')
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light')
  const [mounted, setMounted] = useState(false)

  // Debug theme changes
  const setThemeWithLogging = (newTheme: Theme) => {
    console.log('ThemeProvider: setTheme called with:', newTheme)
    setTheme(newTheme)
  }

  useEffect(() => {
    setMounted(true)
    // Load theme from localStorage on mount
    const savedTheme = localStorage.getItem('theme') as Theme
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      setTheme(savedTheme)
    } else {
      // Default to light theme if no saved theme
      setTheme('light')
    }
  }, [])

  useEffect(() => {
    console.log('ThemeProvider: theme effect triggered', { theme, mounted })

    if (!mounted) return

    const root = window.document.documentElement

    // Remove existing theme classes
    root.classList.remove('light', 'dark')

    let effectiveTheme: 'light' | 'dark'

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      effectiveTheme = systemTheme
    } else {
      effectiveTheme = theme
    }

    console.log('ThemeProvider: applying theme', { theme, effectiveTheme })

    // Apply the theme
    root.classList.add(effectiveTheme)
    setActualTheme(effectiveTheme)

    console.log('ThemeProvider: DOM classes after applying theme:', root.className)

    // Save to localStorage
    localStorage.setItem('theme', theme)
  }, [theme, mounted])

  useEffect(() => {
    if (!mounted) return

    // Listen for system theme changes when theme is set to 'system'
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

      const handleChange = (e: MediaQueryListEvent) => {
        const root = window.document.documentElement
        root.classList.remove('light', 'dark')
        const systemTheme = e.matches ? 'dark' : 'light'
        root.classList.add(systemTheme)
        setActualTheme(systemTheme)
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [theme, mounted])

  const value = {
    theme,
    setTheme: setThemeWithLogging,
    actualTheme,
    mounted,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
