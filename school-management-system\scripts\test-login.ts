import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function testLogin() {
  console.log('🧪 Testing login functionality...')
  
  try {
    // Test credentials
    const testCredentials = [
      { email: '<EMAIL>', password: 'Admin@12345', role: 'ADMIN' },
      { email: '<EMAIL>', password: 'Teacher@12345', role: 'TEACHER' },
      { email: '<EMAIL>', password: 'Student@12345', role: 'STUDENT' }
    ]

    for (const cred of testCredentials) {
      console.log(`\n🔐 Testing login for ${cred.role}: ${cred.email}`)
      
      // Step 1: Find user in database
      const user = await prisma.user.findUnique({
        where: { email: cred.email }
      })

      if (!user) {
        console.log(`❌ User not found in database`)
        continue
      }

      console.log(`✅ User found: ${user.firstName} ${user.lastName}`)

      // Step 2: Verify password
      if (!user.hashedPassword) {
        console.log(`❌ No password hash found`)
        continue
      }

      const isPasswordValid = await bcrypt.compare(cred.password, user.hashedPassword)
      console.log(`${isPasswordValid ? '✅' : '❌'} Password verification: ${isPasswordValid ? 'Valid' : 'Invalid'}`)

      // Step 3: Check role
      const roleMatches = user.role === cred.role
      console.log(`${roleMatches ? '✅' : '❌'} Role verification: Expected ${cred.role}, Got ${user.role}`)

      // Step 4: Simulate NextAuth response
      if (isPasswordValid && roleMatches) {
        const authUser = {
          id: user.id,
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
        console.log(`✅ Login would succeed. Auth user object:`, authUser)
      } else {
        console.log(`❌ Login would fail`)
      }
    }

    // Test invalid credentials
    console.log(`\n🚫 Testing invalid credentials...`)
    const invalidUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (invalidUser) {
      const invalidPassword = await bcrypt.compare('WrongPassword', invalidUser.hashedPassword)
      console.log(`❌ Invalid password test: ${invalidPassword ? 'Unexpectedly valid' : 'Correctly invalid'}`)
    }

  } catch (error) {
    console.error('❌ Error during login test:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testLogin()
