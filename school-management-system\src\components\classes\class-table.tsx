'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Class {
  id: number;
  name: string;
  capacity?: number;
  academicYear?: string;
  isActive: boolean;
  section: {
    id: number;
    name: string;
  };
  teacher?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  _count: {
    students: number;
  };
}

interface ClassTableProps {
  classes: Class[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
  onSearch: (search: string) => void;
  onFilter: (filter: string) => void;
  loading?: boolean;
}

export default function ClassTable({
  classes,
  pagination,
  onPageChange,
  onSearch,
  onFilter,
  loading = false,
}: ClassTableProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<string>('all');
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    onSearch(value);
  };

  const handleFilter = (value: string) => {
    setFilterActive(value);
    onFilter(value);
  };

  const handleDelete = async (classId: number) => {
    if (!confirm('Are you sure you want to delete this class? This action cannot be undone.')) {
      return;
    }

    setDeleteLoading(classId);
    try {
      const response = await fetch(`/api/admin/classes/${classId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete class');
      }

      // Refresh the page to show updated data
      window.location.reload();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setDeleteLoading(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle>Classes ({pagination.total})</CardTitle>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Input
              placeholder="Search classes..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full sm:w-64"
            />
            <select
              value={filterActive}
              onChange={(e) => handleFilter(e.target.value)}
              className="w-full sm:w-auto p-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Classes</option>
              <option value="true">Active Only</option>
              <option value="false">Inactive Only</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {classes.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No classes found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Class Name</th>
                  <th className="text-left p-2">Section</th>
                  <th className="text-left p-2">Teacher</th>
                  <th className="text-left p-2">Students</th>
                  <th className="text-left p-2">Capacity</th>
                  <th className="text-left p-2">Academic Year</th>
                  <th className="text-left p-2">Status</th>
                  <th className="text-left p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {classes.map((classItem) => (
                  <tr key={classItem.id} className="border-b hover:bg-gray-50">
                    <td className="p-2">
                      <div className="font-medium">{classItem.name}</div>
                    </td>
                    <td className="p-2">
                      <Badge variant="secondary">{classItem.section.name}</Badge>
                    </td>
                    <td className="p-2">
                      {classItem.teacher ? (
                        <div>
                          <div className="font-medium">
                            {classItem.teacher.firstName} {classItem.teacher.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{classItem.teacher.email}</div>
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">Not assigned</span>
                      )}
                    </td>
                    <td className="p-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{classItem._count.students}</span>
                        {classItem.capacity && (
                          <span className="text-gray-500">/ {classItem.capacity}</span>
                        )}
                      </div>
                    </td>
                    <td className="p-2">
                      {classItem.capacity || '-'}
                    </td>
                    <td className="p-2">
                      {classItem.academicYear || '-'}
                    </td>
                    <td className="p-2">
                      <Badge variant={classItem.isActive ? 'default' : 'secondary'}>
                        {classItem.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="p-2">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/admin/classes/${classItem.id}`)}
                        >
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/admin/classes/${classItem.id}/edit`)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(classItem.id)}
                          disabled={deleteLoading === classItem.id}
                        >
                          {deleteLoading === classItem.id ? 'Deleting...' : 'Delete'}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-gray-600">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} classes
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                Previous
              </Button>
              <span className="px-3 py-2 text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
