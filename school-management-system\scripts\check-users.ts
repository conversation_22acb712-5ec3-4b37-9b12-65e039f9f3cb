import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function checkUsers() {
  console.log('🔍 Checking database users...')
  
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        hashedPassword: true
      }
    })

    console.log(`\n📊 Found ${users.length} users in database:`)
    
    for (const user of users) {
      console.log(`\n👤 User: ${user.firstName} ${user.lastName}`)
      console.log(`   Email: ${user.email}`)
      console.log(`   Role: ${user.role}`)
      console.log(`   ID: ${user.id}`)
      console.log(`   Has Password: ${user.hashedPassword ? 'Yes' : 'No'}`)
      
      // Test password verification for demo accounts
      if (user.email === '<EMAIL>') {
        const isValid = await bcrypt.compare('Admin@12345', user.hashedPassword)
        console.log(`   Password Test (Admin@12345): ${isValid ? '✅ Valid' : '❌ Invalid'}`)
      } else if (user.email === '<EMAIL>') {
        const isValid = await bcrypt.compare('Teacher@12345', user.hashedPassword)
        console.log(`   Password Test (Teacher@12345): ${isValid ? '✅ Valid' : '❌ Invalid'}`)
      } else if (user.email === '<EMAIL>') {
        const isValid = await bcrypt.compare('Student@12345', user.hashedPassword)
        console.log(`   Password Test (Student@12345): ${isValid ? '✅ Valid' : '❌ Invalid'}`)
      }
    }

    // Test specific demo accounts
    console.log('\n🎯 Testing specific demo accounts:')
    
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    console.log(`Admin account exists: ${adminUser ? '✅ Yes' : '❌ No'}`)
    
    const teacherUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    console.log(`Teacher account exists: ${teacherUser ? '✅ Yes' : '❌ No'}`)
    
    const studentUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    console.log(`Student account exists: ${studentUser ? '✅ Yes' : '❌ No'}`)

  } catch (error) {
    console.error('❌ Error checking users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()
