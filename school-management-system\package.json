{"name": "school-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "test": "vitest", "test:e2e": "playwright test", "reports:generate": "tsx scripts/generate-report-cards.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.15.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@types/bcryptjs": "^3.0.0", "@types/nodemailer": "^7.0.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "next": "15.5.2", "next-auth": "^4.24.11", "nodemailer": "^7.0.6", "playwright": "^1.55.0", "prisma": "^6.15.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^5.0.2", "eslint": "^9", "eslint-config-next": "15.5.2", "prettier": "^3.6.2", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5", "vitest": "^3.2.4"}}