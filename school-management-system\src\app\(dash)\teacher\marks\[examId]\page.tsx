'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { teacherNavigation } from '@/lib/navigation'
import MarksEntryForm from '@/components/marks/marks-entry-form'
import {
  Calendar,
  Award,
  BookOpen,
  Users,
  ArrowLeft,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'

interface Student {
  id: string
  admissionNo: string
  rollNumber: string
  firstName: string
  lastName: string
  email: string
  className: string
  sectionName: string
  currentMark: {
    id: string
    obtainedMarks: number
    remarks?: string
  } | null
  hasMarks: boolean
}

interface Exam {
  id: string
  name: string
  maxMarks: number
  date: string
  subject: {
    id: string
    name: string
    code: string
    class: {
      id: string
      name: string
    }
  }
  term: {
    id: string
    name: string
  }
}

interface ExamData {
  exam: Exam
  students: Student[]
}



export default function TeacherMarksEntryPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const examId = params.examId as string

  const [examData, setExamData] = useState<ExamData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    fetchExamData()
  }, [examId])

  const fetchExamData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/teacher/exams/${examId}/students`)
      if (response.ok) {
        const data = await response.json()
        setExamData(data)
      } else {
        console.error('Failed to fetch exam data')
      }
    } catch (error) {
      console.error('Error fetching exam data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveMarks = async (marksData: Array<{studentId: string, obtainedMarks: number, remarks?: string}>) => {
    try {
      setSaving(true)

      const response = await fetch('/api/teacher/marks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          examId,
          marks: marksData
        })
      })

      if (response.ok) {
        const result = await response.json()
        const successCount = result.results.filter((r: any) => r.success).length
        const errorCount = result.results.filter((r: any) => !r.success).length

        if (errorCount === 0) {
          alert(`Successfully saved marks for ${successCount} students`)
          fetchExamData() // Refresh data
        } else {
          alert(`Saved marks for ${successCount} students. ${errorCount} failed.`)
          console.error('Some marks failed to save:', result.results.filter((r: any) => !r.success))
        }
      } else {
        const error = await response.json()
        alert(`Failed to save marks: ${error.error}`)
      }
    } catch (error) {
      console.error('Error saving marks:', error)
      alert('Failed to save marks. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Enter Marks" navigation={teacherNavigation}>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading exam data...</p>
        </div>
      </DashboardLayout>
    )
  }

  if (!examData) {
    return (
      <DashboardLayout title="Enter Marks" navigation={teacherNavigation}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-gray-600">Exam not found</p>
          <Link href="/teacher/marks">
            <Button className="mt-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Marks
            </Button>
          </Link>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Enter Marks" navigation={teacherNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Link href="/teacher/marks">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Back
                </Button>
              </Link>
            </div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Enter Marks</h1>
            <p className="text-sm sm:text-base text-gray-600">
              {examData.exam.name} - {examData.exam.subject.name} ({examData.exam.subject.class.name})
            </p>
          </div>
        </div>

        {/* Exam Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5" />
              <span>Exam Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Subject:</span>
                <p className="text-gray-600">{examData.exam.subject.name}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Class:</span>
                <p className="text-gray-600">{examData.exam.subject.class.name}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Max Marks:</span>
                <p className="text-gray-600">{examData.exam.maxMarks}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Date:</span>
                <p className="text-gray-600">{new Date(examData.exam.date).toLocaleDateString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Marks Entry Form */}
        <MarksEntryForm
          exam={examData.exam}
          students={examData.students}
          onSave={handleSaveMarks}
          saving={saving}
        />
      </div>
    </DashboardLayout>
  )
}
