import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';

// GET /api/student/attendance - Get student's attendance records
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (session.user.role === 'STUDENT') {
      // Get student ID from the database
      const student = await prisma.student.findUnique({
        where: { userId: session.user.id }
      });
      
      if (!student) {
        return NextResponse.json(
          { error: 'Student profile not found' },
          { status: 404 }
        );
      }
      
      where.studentId = student.id;
    }

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }

    // Get attendance records with pagination
    const [attendanceRecords, total] = await Promise.all([
      prisma.attendance.findMany({
        where,
        skip,
        take: limit,
        orderBy: { date: 'desc' },
        include: {
          class: {
            select: {
              id: true,
              name: true,
              section: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      }),
      prisma.attendance.count({ where }),
    ]);

    // Get student ID for statistics
    const student = await prisma.student.findUnique({
      where: { userId: session.user.id }
    });
    
    if (!student) {
      return NextResponse.json(
        { error: 'Student profile not found' },
        { status: 404 }
      );
    }

    // Calculate attendance statistics
    const totalRecords = await prisma.attendance.count({
      where: {
        studentId: student.id,
        ...(startDate && endDate && {
          date: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      },
    });

    const presentCount = await prisma.attendance.count({
      where: {
        studentId: student.id,
        status: 'PRESENT',
        ...(startDate && endDate && {
          date: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      },
    });

    const absentCount = await prisma.attendance.count({
      where: {
        studentId: student.id,
        status: 'ABSENT',
        ...(startDate && endDate && {
          date: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      },
    });

    const lateCount = await prisma.attendance.count({
      where: {
        studentId: student.id,
        status: 'LATE',
        ...(startDate && endDate && {
          date: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      },
    });

    const halfDayCount = await prisma.attendance.count({
      where: {
        studentId: student.id,
        status: 'HALF_DAY',
        ...(startDate && endDate && {
          date: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      },
    });

    const totalPages = Math.ceil(total / limit);

    const attendancePercentage = totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0;

    return NextResponse.json({
      attendanceRecords,
      statistics: {
        total: totalRecords,
        present: presentCount,
        absent: absentCount,
        late: lateCount,
        halfDay: halfDayCount,
        percentage: Math.round(attendancePercentage * 100) / 100,
      },
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching student attendance:', error);
    return NextResponse.json(
      { error: 'Failed to fetch attendance' },
      { status: 500 }
    );
  }
}
