// Simple test to check NextAuth API endpoints
// Using built-in fetch (Node.js 18+)

async function testNextAuthAPI() {
  const baseUrl = 'http://localhost:3001';
  
  console.log('🧪 Testing NextAuth API endpoints...\n');

  try {
    // Test 1: Check providers endpoint
    console.log('1️⃣ Testing /api/auth/providers');
    const providersResponse = await fetch(`${baseUrl}/api/auth/providers`);
    const providers = await providersResponse.json();
    console.log('✅ Providers response:', JSON.stringify(providers, null, 2));

    // Test 2: Check CSRF endpoint
    console.log('\n2️⃣ Testing /api/auth/csrf');
    const csrfResponse = await fetch(`${baseUrl}/api/auth/csrf`);
    const csrf = await csrfResponse.json();
    console.log('✅ CSRF response:', csrf);

    // Test 3: Test credentials login
    console.log('\n3️⃣ Testing credentials login');
    const loginResponse = await fetch(`${baseUrl}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': csrf.csrfToken
      },
      body: new URLSearchParams({
        email: '<EMAIL>',
        password: 'Admin@12345',
        csrfToken: csrf.csrfToken,
        callbackUrl: `${baseUrl}/admin`,
        json: 'true'
      })
    });

    console.log('📊 Login response status:', loginResponse.status);
    console.log('📊 Login response headers:', Object.fromEntries(loginResponse.headers.entries()));
    
    const loginResult = await loginResponse.text();
    console.log('📊 Login response body:', loginResult);

  } catch (error) {
    console.error('❌ Error testing NextAuth API:', error);
  }
}

testNextAuthAPI();
