'use client'

import { useState } from 'react'
import { signIn, useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestLoginPage() {
  const { data: session, status } = useSession()
  const [credentials, setCredentials] = useState({
    email: '<EMAIL>',
    password: 'Admin@12345'
  })
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleDirectSignIn = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        redirect: false
      })
      
      setResult(result)
    } catch (error) {
      setResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testCredentials = [
    { email: '<EMAIL>', password: 'Admin@12345', role: 'Admin' },
    { email: '<EMAIL>', password: 'Teacher@12345', role: 'Teacher' },
    { email: '<EMAIL>', password: 'Student@12345', role: 'Student' }
  ]

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Login Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Current Session Status:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm">
                Status: {status}
                {session && (
                  <>
                    <br />User: {JSON.stringify(session.user, null, 2)}
                  </>
                )}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Test Direct SignIn:</h3>
              <div className="space-y-2">
                <Input
                  placeholder="Email"
                  value={credentials.email}
                  onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                />
                <Input
                  type="password"
                  placeholder="Password"
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                />
                <Button onClick={handleDirectSignIn} disabled={loading}>
                  {loading ? 'Testing...' : 'Test SignIn'}
                </Button>
              </div>
              
              {result && (
                <div className="mt-4">
                  <h4 className="font-semibold">SignIn Result:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-sm">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </div>
              )}
            </div>

            <div>
              <h3 className="font-semibold mb-2">Quick Test Buttons:</h3>
              <div className="space-y-2">
                {testCredentials.map((cred, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    onClick={() => {
                      setCredentials({ email: cred.email, password: cred.password })
                      setTimeout(() => handleDirectSignIn(), 100)
                    }}
                    disabled={loading}
                  >
                    Test {cred.role} Login
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Navigation Links:</h3>
              <div className="space-x-2">
                <Button variant="outline" onClick={() => window.location.href = '/login'}>
                  Go to Login Page
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/admin'}>
                  Go to Admin Dashboard
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/teacher'}>
                  Go to Teacher Dashboard
                </Button>
                <Button variant="outline" onClick={() => window.location.href = '/student'}>
                  Go to Student Dashboard
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
