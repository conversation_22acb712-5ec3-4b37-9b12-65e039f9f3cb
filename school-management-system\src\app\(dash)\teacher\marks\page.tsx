'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { teacherNavigation } from '@/lib/navigation'
import {
  Calendar,
  Award,
  BookOpen,
  Users,
  Plus,
  Eye,
  Edit,
  FileText
} from 'lucide-react'
import Link from 'next/link'

interface Exam {
  id: string
  name: string
  maxMarks: number
  date: string
  subject: {
    id: string
    name: string
    code: string
    class: {
      id: string
      name: string
    }
  }
  term: {
    id: string
    name: string
  }
  _count: {
    marks: number
  }
}



export default function TeacherMarksPage() {
  const { data: session } = useSession()
  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTerm, setSelectedTerm] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')

  useEffect(() => {
    fetchExams()
  }, [selectedTerm, selectedSubject])

  const fetchExams = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedTerm !== 'all') params.append('termId', selectedTerm)
      if (selectedSubject !== 'all') params.append('subjectId', selectedSubject)

      const response = await fetch(`/api/teacher/exams?${params}`)
      if (response.ok) {
        const data = await response.json()
        setExams(data)
      } else {
        console.error('Failed to fetch exams')
      }
    } catch (error) {
      console.error('Error fetching exams:', error)
    } finally {
      setLoading(false)
    }
  }

  const marksStats = {
    totalExams: exams.length,
    gradedExams: exams.filter(exam => exam._count.marks > 0).length,
    pendingExams: exams.filter(exam => exam._count.marks === 0).length,
    totalMarksEntered: exams.reduce((sum, exam) => sum + exam._count.marks, 0)
  }

  return (
    <DashboardLayout title="Marks Management" navigation={teacherNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Marks Management</h1>
            <p className="text-sm sm:text-base text-gray-600">Enter and manage examination marks</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{marksStats.totalExams}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Graded Exams</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.gradedExams}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Exams</CardTitle>
              <Award className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{marksStats.pendingExams}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Marks Entered</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{marksStats.totalMarksEntered}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="term" className="block text-sm font-medium text-gray-700 mb-2">
                  Term
                </label>
                <select
                  id="term"
                  value={selectedTerm}
                  onChange={(e) => setSelectedTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Terms</option>
                  <option value="term1">Term 1</option>
                  <option value="term2">Term 2</option>
                  <option value="term3">Term 3</option>
                </select>
              </div>
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  Subject
                </label>
                <select
                  id="subject"
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Subjects</option>
                  <option value="math">Mathematics</option>
                  <option value="english">English</option>
                  <option value="science">Science</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Exams List */}
        <Card>
          <CardHeader>
            <CardTitle>Available Exams</CardTitle>
            <CardDescription>
              Select an exam to enter or view marks
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading exams...</p>
              </div>
            ) : exams.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No exams found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {exams.map((exam) => (
                  <div key={exam.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                      <div className="flex items-start space-x-3">
                        <BookOpen className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {exam.name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {exam.subject.name} • {exam.subject.class.name} • {exam.term.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            Max Marks: {exam.maxMarks} • Date: {new Date(exam.date).toLocaleDateString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            Marks Entered: {exam._count.marks} students
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Link href={`/teacher/marks/${exam.id}`}>
                          <Button variant="outline" size="sm">
                            <Edit className="w-4 h-4 mr-1" />
                            Enter Marks
                          </Button>
                        </Link>
                        <Link href={`/teacher/marks/${exam.id}/view`}>
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            View Marks
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
