'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { 
  Calendar, 
  Plus, 
  Edit, 
  Trash2, 
  BookOpen,
  Clock,
  Award
} from 'lucide-react'

interface Term {
  id: string
  name: string
  startDate: string
  endDate: string
  academicYear: string
}

interface Exam {
  id: string
  name: string
  termName: string
  subjectName: string
  maxMarks: number
  weightagePercent: number
  date: string
}

import { adminNavigation } from '@/lib/navigation';

export default function ExamsPage() {
  const [activeTab, setActiveTab] = useState<'terms' | 'exams'>('terms')
  const [terms, setTerms] = useState<Term[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [showAddTerm, setShowAddTerm] = useState(false)
  const [showAddExam, setShowAddExam] = useState(false)

  // Mock data - in real app, this would come from API
  useEffect(() => {
    setTerms([
      {
        id: '1',
        name: 'Term 1',
        startDate: '2024-09-01',
        endDate: '2024-12-15',
        academicYear: '2024-2025'
      },
      {
        id: '2',
        name: 'Term 2',
        startDate: '2025-01-15',
        endDate: '2025-04-30',
        academicYear: '2024-2025'
      }
    ])

    setExams([
      {
        id: '1',
        name: 'Unit Test 1',
        termName: 'Term 1',
        subjectName: 'Mathematics',
        maxMarks: 50,
        weightagePercent: 20,
        date: '2024-10-15'
      },
      {
        id: '2',
        name: 'Mid Term Exam',
        termName: 'Term 1',
        subjectName: 'English',
        maxMarks: 100,
        weightagePercent: 40,
        date: '2024-11-20'
      }
    ])
  }, [])

  return (
    <DashboardLayout title="Terms & Exams Management" navigation={adminNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Terms & Exams</h1>
            <p className="text-gray-600">Manage academic terms and examinations</p>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('terms')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'terms'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Calendar className="inline w-4 h-4 mr-2" />
              Academic Terms
            </button>
            <button
              onClick={() => setActiveTab('exams')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'exams'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <BookOpen className="inline w-4 h-4 mr-2" />
              Examinations
            </button>
          </nav>
        </div>

        {/* Terms Tab */}
        {activeTab === 'terms' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">Academic Terms</h2>
              <Button onClick={() => setShowAddTerm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Term
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {terms.map((term) => (
                <Card key={term.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                      {term.name}
                    </CardTitle>
                    <CardDescription>{term.academicYear}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Start Date:</span>
                        <span className="text-sm font-medium">{new Date(term.startDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">End Date:</span>
                        <span className="text-sm font-medium">{new Date(term.endDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2 mt-4">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Exams Tab */}
        {activeTab === 'exams' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">Examinations</h2>
              <Button onClick={() => setShowAddExam(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Exam
              </Button>
            </div>

            <div className="bg-white rounded-lg border">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Exam Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Term
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Subject
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Max Marks
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Weightage
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {exams.map((exam) => (
                      <tr key={exam.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <BookOpen className="w-4 h-4 mr-2 text-blue-600" />
                            <span className="text-sm font-medium text-gray-900">{exam.name}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {exam.termName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {exam.subjectName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {exam.maxMarks}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {exam.weightagePercent}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(exam.date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
