# Dark Mode Implementation

This document describes the dark mode implementation in the School Management System.

## Features

- **Theme Toggle**: Users can switch between light, dark, and system themes
- **Persistent Storage**: Theme preference is saved in localStorage
- **System Theme Detection**: Automatically detects and follows system theme preference
- **Smooth Transitions**: CSS transitions for smooth theme switching
- **Comprehensive Coverage**: All UI components support dark mode

## Components Added

### 1. Theme Provider (`src/components/providers/theme-provider.tsx`)
- Manages theme state across the application
- Handles localStorage persistence
- Listens for system theme changes
- Provides theme context to all components

### 2. Theme Toggle Components
- **SimpleThemeToggle** (`src/components/ui/simple-theme-toggle.tsx`): Simple toggle between light/dark
- **ThemeToggle** (`src/components/ui/theme-toggle.tsx`): Dropdown with light/dark/system options

### 3. Updated UI Components
All UI components have been updated to support dark mode:
- `Button` - Updated with dark mode variants
- `Input` - Dark background and text colors
- `Alert` - Dark mode styling for all variants
- `Badge` - Dark mode support for all badge types
- `Card` - Uses CSS variables for theming
- `Dashboard Layout` - Dark backgrounds and proper contrast

## Usage

### Basic Theme Toggle
```tsx
import { SimpleThemeToggle } from '@/components/ui/simple-theme-toggle'

function Header() {
  return (
    <div>
      <SimpleThemeToggle />
    </div>
  )
}
```

### Advanced Theme Toggle with System Option
```tsx
import { ThemeToggle } from '@/components/ui/theme-toggle'

function Settings() {
  return (
    <div>
      <ThemeToggle />
    </div>
  )
}
```

### Using Theme in Components
```tsx
import { useTheme } from '@/components/providers/theme-provider'

function MyComponent() {
  const { theme, setTheme, actualTheme } = useTheme()
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Actual theme: {actualTheme}</p>
      <button onClick={() => setTheme('dark')}>
        Switch to Dark
      </button>
    </div>
  )
}
```

## Theme Options

- **light**: Light theme
- **dark**: Dark theme  
- **system**: Follows system preference

## CSS Variables

The implementation uses CSS variables defined in `globals.css`:

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* ... other variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... other variables */
}
```

## Tailwind Configuration

Dark mode is configured in `tailwind.config.ts`:

```typescript
export default {
  darkMode: "class", // Uses class-based dark mode
  // ... rest of config
}
```

## Integration

The theme provider is integrated at the root level in `src/components/providers/session-provider.tsx` and wraps the entire application.

## Browser Support

- Modern browsers with CSS custom properties support
- localStorage support for theme persistence
- matchMedia API for system theme detection
