// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User model - Base authentication entity
model User {
  id             String   @id @default(cuid())
  email          String   @unique
  hashedPassword String
  role           UserRole @default(STUDENT)
  firstName      String
  lastName       String
  phone          String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  student Student?
  teacher Teacher?
  auditLogs AuditLog[]

  @@map("users")
}

// Student model - Extended user profile for students
model Student {
  id              String   @id @default(cuid())
  userId          String   @unique
  admissionNo     String   @unique
  dob             DateTime
  gender          Gender
  address         String?
  guardianName    String
  guardianPhone   String
  currentClassId  String?
  currentSectionId String?
  rollNumber      String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentClass    Class?   @relation("CurrentClass", fields: [currentClassId], references: [id])
  currentSection  Section? @relation("CurrentSection", fields: [currentSectionId], references: [id])
  enrollments     Enrollment[]
  attendances     Attendance[]
  marks           Mark[]
  reportCards     ReportCard[]

  @@map("students")
}

// Teacher model - Extended user profile for teachers
model Teacher {
  id           String   @id @default(cuid())
  userId       String   @unique
  employeeCode String   @unique
  dateOfBirth  DateTime?
  gender       Gender?
  address      String?
  qualification String?
  experience   Int?     // Years of experience
  joinedOn     DateTime @default(now())
  salary       Float?
  isActive     Boolean  @default(true)
  phoneAlt     String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  attendances  Attendance[]
  marks        Mark[]

  @@map("teachers")
}

// Class model - School classes/grades
model Class {
  id        String   @id @default(cuid())
  name      String   @unique // e.g., "Grade 8"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  sections      Section[]
  subjects      Subject[]
  enrollments   Enrollment[]
  students      Student[] @relation("CurrentClass")
  attendances   Attendance[]

  @@map("classes")
}

// Section model - Sections within classes
model Section {
  id      String @id @default(cuid())
  name    String // e.g., "A", "B"
  classId String

  // Relations
  class       Class        @relation(fields: [classId], references: [id], onDelete: Cascade)
  enrollments Enrollment[]
  students    Student[]    @relation("CurrentSection")
  attendances Attendance[]

  @@unique([name, classId])
  @@map("sections")
}

// Subject model - Academic subjects
model Subject {
  id      String @id @default(cuid())
  name    String // e.g., "Mathematics"
  code    String @unique
  classId String

  // Relations
  class       Class        @relation(fields: [classId], references: [id], onDelete: Cascade)
  exams       Exam[]
  attendances Attendance[]

  @@map("subjects")
}

// Enrollment model - Student enrollment in classes
model Enrollment {
  id           String @id @default(cuid())
  studentId    String
  classId      String
  sectionId    String
  academicYear String // e.g., "2024-2025"
  active       Boolean @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  class   Class   @relation(fields: [classId], references: [id], onDelete: Cascade)
  section Section @relation(fields: [sectionId], references: [id], onDelete: Cascade)

  @@unique([studentId, classId, sectionId, academicYear])
  @@map("enrollments")
}

// Term model - Academic terms
model Term {
  id           String   @id @default(cuid())
  name         String   // e.g., "Term 1"
  startDate    DateTime
  endDate      DateTime
  academicYear String   // e.g., "2024-2025"
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  exams        Exam[]
  reportCards  ReportCard[]

  @@unique([name, academicYear])
  @@map("terms")
}

// Exam model - Examinations within terms
model Exam {
  id               String   @id @default(cuid())
  name             String   // e.g., "Unit Test 1"
  termId           String
  subjectId        String
  maxMarks         Int
  weightagePercent Float    @default(1.0)
  date             DateTime
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  term   Term   @relation(fields: [termId], references: [id], onDelete: Cascade)
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  marks  Mark[]

  @@map("exams")
}

// Attendance model - Student attendance records
model Attendance {
  id                String        @id @default(cuid())
  studentId         String
  date              DateTime
  status            AttendanceStatus
  remarks           String?
  takenByTeacherId  String?
  classId           String
  sectionId         String
  subjectId         String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  student        Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  takenByTeacher Teacher? @relation(fields: [takenByTeacherId], references: [id])
  class          Class    @relation(fields: [classId], references: [id], onDelete: Cascade)
  section        Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  subject        Subject? @relation(fields: [subjectId], references: [id])

  @@unique([studentId, date, classId, sectionId])
  @@map("attendances")
}

// Mark model - Student examination marks
model Mark {
  id               String   @id @default(cuid())
  studentId        String
  examId           String
  obtainedMarks    Float
  gradedByTeacherId String?
  remarks          String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  student        Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  exam           Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  gradedByTeacher Teacher? @relation(fields: [gradedByTeacherId], references: [id])

  @@unique([studentId, examId])
  @@map("marks")
}

// ReportCard model - Generated report cards
model ReportCard {
  id           String   @id @default(cuid())
  studentId    String
  termId       String
  jsonSnapshot Json     // Store complete report data as JSON
  pdfPath      String?  // Path to generated PDF file
  generatedAt  DateTime @default(now())

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  term    Term    @relation(fields: [termId], references: [id], onDelete: Cascade)

  @@unique([studentId, termId])
  @@map("report_cards")
}

// AuditLog model - System audit trail
model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String   // e.g., "CREATE", "UPDATE", "DELETE"
  entity    String   // e.g., "Student", "Mark"
  entityId  String?
  meta      Json?    // Additional metadata
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("audit_logs")
}

// Setting model - System configuration
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

// Enums
enum UserRole {
  ADMIN
  TEACHER
  STUDENT
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
}
