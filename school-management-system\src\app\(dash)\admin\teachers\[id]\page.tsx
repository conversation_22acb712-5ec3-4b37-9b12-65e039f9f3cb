'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Teacher {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  qualification?: string;
  experience?: number;
  joiningDate?: string;
  salary?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: {
    id: number;
    email: string;
    role: string;
  };
  classes: Array<{
    id: number;
    name: string;
    section: {
      id: number;
      name: string;
    };
  }>;
  subjects: Array<{
    id: number;
    name: string;
  }>;
}

export default function TeacherDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [teacher, setTeacher] = useState<Teacher | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [teacherId, setTeacherId] = useState<string | null>(null);

  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = await params;
        setTeacherId(resolvedParams.id);
      } catch (err) {
        setError('Failed to resolve route parameters');
        setLoading(false);
      }
    };

    resolveParams();
  }, [params]);

  useEffect(() => {
    if (!teacherId) return;

    const fetchTeacher = async () => {
      try {
        const response = await fetch(`/api/admin/teachers/${teacherId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch teacher');
        }
        const data = await response.json();
        setTeacher(data.teacher);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTeacher();
  }, [teacherId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'MALE': return 'Male';
      case 'FEMALE': return 'Female';
      case 'OTHER': return 'Other';
      default: return 'Not specified';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!teacher) {
    return (
      <Alert>
        <AlertDescription>Teacher not found</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {teacher.firstName} {teacher.lastName}
          </h1>
          <p className="text-gray-600">{teacher.email}</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/admin/teachers/${teacher.id}/edit`)}
          >
            Edit Teacher
          </Button>
          <Button onClick={() => router.push('/admin/teachers')}>
            Back to List
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-lg">{teacher.firstName} {teacher.lastName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-lg">{teacher.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Phone</label>
              <p className="text-lg">{teacher.phone || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Gender</label>
              <p className="text-lg">{teacher.gender ? getGenderLabel(teacher.gender) : 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Date of Birth</label>
              <p className="text-lg">
                {teacher.dateOfBirth ? formatDate(teacher.dateOfBirth) : 'Not provided'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Address</label>
              <p className="text-lg">{teacher.address || 'Not provided'}</p>
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Professional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Qualification</label>
              <p className="text-lg">{teacher.qualification || 'Not provided'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Experience</label>
              <p className="text-lg">
                {teacher.experience ? `${teacher.experience} years` : 'Not specified'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Joining Date</label>
              <p className="text-lg">
                {teacher.joiningDate ? formatDate(teacher.joiningDate) : 'Not provided'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Salary</label>
              <p className="text-lg">
                {teacher.salary ? `$${teacher.salary.toLocaleString()}` : 'Not specified'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <div className="mt-1">
                <Badge variant={teacher.isActive ? 'default' : 'secondary'}>
                  {teacher.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">User ID</label>
              <p className="text-lg">{teacher.user.id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Role</label>
              <p className="text-lg">{teacher.user.role}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Created</label>
              <p className="text-lg">{formatDate(teacher.createdAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-lg">{formatDate(teacher.updatedAt)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Classes and Subjects */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Assigned Classes</CardTitle>
          </CardHeader>
          <CardContent>
            {teacher.classes && teacher.classes.length > 0 ? (
              <div className="space-y-2">
                {teacher.classes.map((cls) => (
                  <Badge key={cls.id} variant="secondary" className="mr-2 mb-2">
                    {cls.name} {cls.section.name}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No classes assigned</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Teaching Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            {teacher.subjects && teacher.subjects.length > 0 ? (
              <div className="space-y-2">
                {teacher.subjects.map((subject) => (
                  <Badge key={subject.id} variant="outline" className="mr-2 mb-2">
                    {subject.name}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No subjects assigned</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
