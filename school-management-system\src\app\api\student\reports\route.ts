import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get student record for the logged-in user
    const student = await prisma.student.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const termId = searchParams.get('termId')

    const where: any = {
      studentId: student.id
    }
    
    if (termId && termId !== 'all') {
      where.termId = termId
    }

    const reportCards = await prisma.reportCard.findMany({
      where,
      include: {
        term: true
      },
      orderBy: { generatedAt: 'desc' }
    })

    return NextResponse.json(reportCards)
  } catch (error) {
    console.error('Error fetching student report cards:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
