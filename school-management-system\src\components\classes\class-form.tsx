'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ClassFormProps {
  classData?: any;
  mode: 'create' | 'edit';
}

interface Section {
  id: number;
  name: string;
  description?: string;
}

interface Teacher {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
}

export default function ClassForm({ classData, mode }: ClassFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [sections, setSections] = useState<Section[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);

  const [formData, setFormData] = useState({
    name: '',
    sectionId: '',
    teacherId: '',
    capacity: '',
    academicYear: '',
    isActive: true,
  });

  useEffect(() => {
    // Fetch sections and teachers
    const fetchData = async () => {
      try {
        const [sectionsRes, teachersRes] = await Promise.all([
          fetch('/api/admin/sections'),
          fetch('/api/admin/teachers'),
        ]);

        if (sectionsRes.ok) {
          const sectionsData = await sectionsRes.json();
          setSections(sectionsData.sections);
        }

        if (teachersRes.ok) {
          const teachersData = await teachersRes.json();
          setTeachers(teachersData.teachers);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (classData) {
      setFormData({
        name: classData.name || '',
        sectionId: classData.sectionId?.toString() || '',
        teacherId: classData.teacherId?.toString() || '',
        capacity: classData.capacity?.toString() || '',
        academicYear: classData.academicYear || '',
        isActive: classData.isActive ?? true,
      });
    }
  }, [classData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const payload = {
        ...formData,
        sectionId: parseInt(formData.sectionId),
        teacherId: formData.teacherId ? parseInt(formData.teacherId) : undefined,
        capacity: formData.capacity ? parseInt(formData.capacity) : undefined,
      };

      const url = mode === 'create' 
        ? '/api/admin/classes' 
        : `/api/admin/classes/${classData.id}`;
      
      const method = mode === 'create' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save class');
      }

      setSuccess(data.message);

      if (mode === 'create') {
        // Reset form after successful creation
        setTimeout(() => {
          router.push('/admin/classes');
        }, 2000);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? 'Add New Class' : 'Edit Class'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Class Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                disabled={loading}
                placeholder="e.g., Grade 8"
              />
            </div>

            <div>
              <Label htmlFor="sectionId">Section *</Label>
              <select
                id="sectionId"
                value={formData.sectionId}
                onChange={(e) => handleInputChange('sectionId', e.target.value)}
                required
                disabled={loading}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Section</option>
                {sections.map((section) => (
                  <option key={section.id} value={section.id}>
                    {section.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="teacherId">Class Teacher</Label>
              <select
                id="teacherId"
                value={formData.teacherId}
                onChange={(e) => handleInputChange('teacherId', e.target.value)}
                disabled={loading}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Teacher</option>
                {teachers.map((teacher) => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.firstName} {teacher.lastName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="capacity">Capacity</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', e.target.value)}
                disabled={loading}
                placeholder="e.g., 30"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="academicYear">Academic Year</Label>
            <Input
              id="academicYear"
              value={formData.academicYear}
              onChange={(e) => handleInputChange('academicYear', e.target.value)}
              disabled={loading}
              placeholder="e.g., 2024-2025"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              id="isActive"
              type="checkbox"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              disabled={loading}
              className="rounded"
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : mode === 'create' ? 'Create Class' : 'Update Class'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
