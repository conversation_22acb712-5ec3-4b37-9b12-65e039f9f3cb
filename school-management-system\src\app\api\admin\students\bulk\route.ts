import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma as db } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import bcrypt from 'bcryptjs';

// GET /api/admin/students/bulk - Export students to CSV
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(session.user.role, 'students:read')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId') || '';

    // Build where clause
    const where: any = {};
    if (classId) {
      where.currentClassId = classId;
    }

    // Fetch all students with related data
    const students = await db.student.findMany({
      where,
      include: {
        user: true,
        currentClass: true,
        currentSection: true,
      },
      orderBy: [
        { user: { lastName: 'asc' } },
        { user: { firstName: 'asc' } },
      ],
    });

    // Convert to CSV format
    const csvHeaders = [
      'ID',
      'First Name',
      'Last Name',
      'Email',
      'Date of Birth',
      'Gender',
      'Phone Number',
      'Address',
      'Emergency Contact',
      'Emergency Phone',
      'Admission Date',
      'Class',
      'Section',
      'Parent Name',
      'Parent Phone',
      'Parent Email',
    ];

    const csvRows = students.map(student => [
      student.id,
      student.user?.firstName || '',
      student.user?.lastName || '',
      student.user?.email || '',
      student.dob.toISOString().split('T')[0],
      student.gender,
      student.user?.phone || '',
      student.address || '',
      student.guardianName || '',
      student.guardianPhone || '',
      student.createdAt.toISOString().split('T')[0],
      student.currentClass?.name || '',
      student.currentSection?.name || '',
      student.guardianName || '',
      student.guardianPhone || '',
      '', // No parent email in schema
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    // Return CSV file
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="students-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });
  } catch (error) {
    console.error('Error exporting students:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/students/bulk - Bulk import students from CSV
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!hasPermission(session.user.role, 'students:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!file.name.endsWith('.csv')) {
      return NextResponse.json(
        { error: 'Only CSV files are allowed' },
        { status: 400 }
      );
    }

    // Read file content
    const text = await file.text();
    const lines = text.split('\n').filter(line => line.trim());

    if (lines.length < 2) {
      return NextResponse.json(
        { error: 'CSV file must have at least a header row and one data row' },
        { status: 400 }
      );
    }

    // Parse CSV headers
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    
    // Expected headers (case-insensitive)
    const expectedHeaders = [
      'first name', 'last name', 'email', 'date of birth', 'gender',
      'phone number', 'address', 'emergency contact', 'emergency phone',
      'admission date', 'class', 'section', 'parent name', 'parent phone', 'parent email'
    ];

    const missingHeaders = expectedHeaders.filter(expected => 
      !headers.some(header => header.toLowerCase() === expected.toLowerCase())
    );

    if (missingHeaders.length > 0) {
      return NextResponse.json(
        { 
          error: 'Missing required headers',
          missingHeaders 
        },
        { status: 400 }
      );
    }

    // Process data rows
    const results = {
      success: 0,
      errors: [] as string[],
      total: lines.length - 1,
    };

    // Get all classes for lookup
    const classes = await db.class.findMany({
      include: {
        sections: true,
      },
    });

    // Process each row
    for (let i = 1; i < lines.length; i++) {
      try {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
        const rowData: any = {};
        
        headers.forEach((header, index) => {
          rowData[header.toLowerCase()] = values[index] || '';
        });

        // Validate required fields
        if (!rowData['first name'] || !rowData['last name'] || !rowData['email']) {
          results.errors.push(`Row ${i + 1}: Missing required fields (first name, last name, or email)`);
          continue;
        }

        // Check if email already exists
        const existingUser = await db.user.findUnique({
          where: { email: rowData['email'] },
        });

        if (existingUser) {
          results.errors.push(`Row ${i + 1}: Email ${rowData['email']} already exists`);
          continue;
        }

        // Find class by name and section
        let classId = null;
        let sectionId = null;
        if (rowData['class'] && rowData['section']) {
          const classRecord = classes.find(c => 
            c.name.toLowerCase() === rowData['class'].toLowerCase()
          );
          
          if (classRecord) {
            const sectionRecord = classRecord.sections.find(s => 
              s.name.toLowerCase() === rowData['section'].toLowerCase()
            );
            
            if (sectionRecord) {
              classId = classRecord.id;
              sectionId = sectionRecord.id;
            }
          }
        }

        if (!classId) {
          results.errors.push(`Row ${i + 1}: Class "${rowData['class']}" Section "${rowData['section']}" not found`);
          continue;
        }

        // Validate gender
        const validGenders = ['MALE', 'FEMALE', 'OTHER'];
        const gender = rowData['gender']?.toUpperCase();
        if (gender && !validGenders.includes(gender)) {
          results.errors.push(`Row ${i + 1}: Invalid gender "${rowData['gender']}"`);
          continue;
        }

        // Validate dates
        const dateOfBirth = rowData['date of birth'] ? new Date(rowData['date of birth']) : null;
        const admissionDate = rowData['admission date'] ? new Date(rowData['admission date']) : null;

        if (dateOfBirth && isNaN(dateOfBirth.getTime())) {
          results.errors.push(`Row ${i + 1}: Invalid date of birth "${rowData['date of birth']}"`);
          continue;
        }

        if (admissionDate && isNaN(admissionDate.getTime())) {
          results.errors.push(`Row ${i + 1}: Invalid admission date "${rowData['admission date']}"`);
          continue;
        }

        // Generate a secure default password
        const defaultPassword = 'Student@12345';
        const hashedPassword = await bcrypt.hash(defaultPassword, 12);
        
        // Create user account
        const user = await db.user.create({
          data: {
            email: rowData['email'],
            hashedPassword: hashedPassword,
            role: 'STUDENT',
            firstName: rowData['first name'],
            lastName: rowData['last name'],
            phone: rowData['phone number'] || null,
          },
        });

        // Create student record
        await db.student.create({
          data: {
            userId: user.id,
            admissionNo: `STU${Date.now()}-${i}`, // Generate unique admission number
            dob: dateOfBirth || new Date(),
            gender: gender || 'OTHER',
            address: rowData['address'] || null,
            guardianName: rowData['parent name'] || 'Guardian',
            guardianPhone: rowData['parent phone'] || '',
            currentClassId: classId,
            currentSectionId: sectionId,
            rollNumber: null,
          },
        });

        results.success++;
      } catch (error) {
        results.errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: 'Bulk import completed',
      results,
    });
  } catch (error) {
    console.error('Error importing students:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
