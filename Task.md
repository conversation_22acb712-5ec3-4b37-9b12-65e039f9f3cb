You are a senior full-stack engineer. Create a production-ready School Management System (SMS) with the following:

# 0) Tech Stack & Project Setup
- Framework: Next.js 14 (App Router) + TypeScript
- UI: Tailwind CSS + shadcn/ui + lucide-react icons
- ORM/DB: Prisma + PostgreSQL
- Auth: NextAuth (Credentials) with role-based access control (RBAC)
- Validation: Zod
- Charts: Recharts
- PDF reports: HTML-to-PDF via <PERSON><PERSON> (headless Chromium) or P<PERSON>peteer (choose <PERSON><PERSON> by default)
- Emails: Nodemailer (dev) with adapter slot for production (e.g., Resend)
- File storage (optional): local in /uploads with easy switch to S3-compatible storage
- Logging/Monitoring: simple audit log table + request logging (middleware)
- Deployment: Dockerfile + compose; docs for Vercel (web) + Neon/Render/Railway (Postgres)
- Testing: Vitest + Playwright (basic smoke tests)
- Package manager: pnpm

Initialize a single Next.js app repo with the above. Configure ESLint/Prettier, absolute imports (@/*), and .nvmrc. Provide a concise README with setup instructions.

# 1) Roles & Permissions
Three roles: ADMIN, TEACHER, STUDENT.
- ADMIN: full access (manage users, classes, subjects, terms, exams, marks, attendance, reports, settings).
- TEACHER: can view assigned classes/subjects, take attendance, enter/edit marks for their subjects, view student profiles limited to their classes, generate per-class reports.
- STUDENT: can log in and view personal profile, attendance history, marks, downloadable report cards.
Implement RBAC middleware that protects both pages and API routes. Centralize permission checks in a helper (e.g., lib/rbac.ts).

# 2) Core Features
A) Authentication & User Management
- NextAuth Credentials with secure password hashing (bcrypt).
- Admin user seeded.
- Sessions stored in DB via Prisma adapter.
- Password reset (email magic link placeholder in dev).
- Profile management for users (name, avatar optional).

B) Entities & Relationships (Prisma)
Design for Indian school context but generic worldwide.
- User(id, email, hashedPassword, role: ADMIN|TEACHER|STUDENT, firstName, lastName, phone, createdAt, updatedAt)
- Student(id, userId FK, admissionNo, dob, gender, address, guardianName, guardianPhone, currentClassId FK, currentSectionId FK, rollNumber)
- Teacher(id, userId FK, employeeCode, qualification, phoneAlt, joinedOn)
- Class(id, name e.g. "Grade 8", createdAt)
- Section(id, name e.g. "A", classId FK)
- Subject(id, name e.g. "Mathematics", code, classId FK)
- Enrollment(id, studentId FK, classId FK, sectionId FK, academicYear, active boolean)
- Timetable (optional stub)
- Attendance(id, studentId, date, status: PRESENT|ABSENT|LATE|HALF_DAY, remarks, takenByTeacherId, classId, sectionId, subjectId nullable)
- Term(id, name e.g. "Term 1", startDate, endDate, academicYear)
- Exam(id, name e.g. "Unit Test 1", termId, subjectId, maxMarks, weightagePercent, date)
- Mark(id, studentId, examId, obtainedMarks, gradedByTeacherId, remarks)
- ReportCard(id, studentId, termId, jsonSnapshot, pdfPath, generatedAt)
- AuditLog(id, userId, action, entity, entityId, meta JSON, createdAt)
- Setting(id, key, value JSON)

Provide full Prisma schema with relations, indexes, and cascade rules. Include seed.ts to create:
- One admin, two teachers, ~10 students across a Grade 8 (Sections A & B), 5 subjects, a Term, a few Exams, attendance for last 7 days, random marks.

C) Admin Panel
- Path: /admin
- Sections (left nav): Dashboard, Students, Teachers, Classes & Sections, Subjects, Terms & Exams, Attendance, Marks, Reports, Settings, Audit Logs.
- CRUD pages for Students/Teachers/Classes/Sections/Subjects/Terms/Exams with searchable/filterable tables (server-side pagination).
- Bulk import CSV for Students and Teachers (template + validation).
- Attendance overview: calendar heatmap per class; export CSV.
- Marks overview by exam/subject/class; bulk entry grid for a class/subject.
- Report generation: pick Term (and optionally class/section), generate all students’ report cards (PDFs) and store file paths + JSON snapshot.

D) Teacher Portal
- Path: /teacher
- Dashboard: assigned classes/subjects, quick actions (Take Attendance, Enter Marks, Generate Class Report).
- Attendance entry: date picker, class/section selector, student list with toggle (Present/Absent/Late/Half) and remarks; save + lock.
- Marks entry: choose exam + class/subject; spreadsheet-style grid (student rows) with validation; autosave; lock after submission (admin unlocks).
- Class reports: weekly/monthly summaries for attendance and marks; export CSV/PDF.

E) Student Portal
- Path: /student
- Dashboard: personal info, current class/section, attendance summary (pie + trend), marks by subject (bar/line), latest announcements (stub).
- Report Cards: list of generated PDFs per Term; view/download.
- Attendance: calendar + list with filters (date range); stats (present %, late %).
- Marks: per subject, per exam breakdown, class average comparison.

F) Reports & Analytics
- Weekly/monthly reports per class/section/term:
  - Attendance: totals, % present, late, absent; trend line; top/bottom attendance.
  - Marks: per subject average, highest/lowest, student ranking for the period; box plot summary (approx via calculated stats).
- Professional-grade Report Card (PDF):
  - Header with school logo/name/address.
  - Student details (name, roll, admission no., class/section, DOB, guardian).
  - Marks table by subject with Max, Obtained, % and grade; teacher remarks.
  - Attendance summary for the term (working days, present, absent).
  - Overall result, GPA/grade (A–E scale).
  - Principal/Teacher signatures area, QR code linking to verification URL (/verify/[reportCardId]).
  - Footer with school contact.
- Implement HTML templates in /app/(reports)/templates and render to PDF via Playwright on the server. Store PDFs to /public/reports (dev) with abstraction to swap to S3.

# 3) API Design (App Router routes under /app/api)
- Auth: NextAuth routes.
- Users: GET/POST/PATCH/DELETE (admin-only).
- Students: CRUD + bulkImport; GET /students?classId=&sectionId=&q=
- Teachers: CRUD + bulkImport.
- Classes/Sections/Subjects: CRUD.
- Attendance:
  - POST /attendance/take (date, classId, sectionId, entries[] of {studentId, status, remarks})
  - GET /attendance/summary?classId=&sectionId=&from=&to=
  - GET /attendance/student/:id?from=&to=
- Exams/Terms: CRUD.
- Marks:
  - POST /marks/bulk (examId, entries[] {studentId, obtainedMarks, remarks})
  - GET /marks/summary?classId=&subjectId=&examId=
  - GET /marks/student/:id?termId=
- Reports:
  - POST /reports/report-card/generate (termId, classId?, sectionId?)
  - GET /reports/report-card/:id (serves PDF)
  - GET /reports/weekly?classId=&sectionId=&from=&to=
  - GET /reports/monthly?classId=&sectionId=&month=YYYY-MM
- Audit logs: list with filters.
- All routes guarded by RBAC. Input validation with Zod. Return JSON; streaming when generating batch PDFs.

# 4) UI/UX Requirements
- Clean dashboard cards; responsive; dark mode toggle.
- Use shadcn/ui for tables, dialogs, forms (react-hook-form + zodResolver).
- Grid-based layouts, rounded-2xl cards, soft shadows, adequate padding.
- Loading/skeleton states; toast notifications for actions.
- Global search (cmd+k) for admin to jump to entities.
- CSV import wizard with preview and column mapping.

# 5) Calculations & Business Rules
- Attendance % = presentDays / workingDays * 100 (exclude holidays placeholder).
- Grade mapping (customizable in Settings):
  - >= 90 A+, >=80 A, >=70 B+, >=60 B, >=50 C, >=40 D, else E
- GPA (optional): weighted by subject credit (default credit=1).
- Exam weightage: use Exam.weightagePercent when aggregating term totals.
- Locking: teachers can "Submit & Lock" attendance/marks; only ADMIN can unlock (logged in AuditLog).
- Consistency checks: obtainedMarks ≤ maxMarks; dates within term; student belongs to class/section.

# 6) Security & Quality
- Hash passwords (bcrypt), CSRF protection via NextAuth, rate-limit auth endpoints.
- Input validation everywhere; server-only secrets via env.
- Server Actions isolated; do not expose secrets to client.
- Add middleware.ts to force auth on protected routes and attach user/role to request.
- Basic unit tests for utils and RBAC; e2e test to log in and generate a sample report card.

# 7) Files to Create (high level)
- prisma/schema.prisma + migrations + seed.ts
- src/lib/{db.ts, rbac.ts, csv.ts, grading.ts, pdf.ts}
- src/app/(auth)/login/page.tsx
- src/app/(dash)/admin/... (all admin pages listed)
- src/app/(dash)/teacher/... (attendance, marks, reports)
- src/app/(dash)/student/... (overview, attendance, marks, report-cards)
- src/app/api/... (as specified in #3)
- src/app/(reports)/templates/{report-card.tsx, class-weekly.tsx, class-monthly.tsx}
- src/middleware.ts
- tailwind, shadcn config
- scripts/generate-report-cards.ts (CLI)
- Dockerfile, docker-compose.yml
- README.md with step-by-step setup

# 8) Env & Scripts
Add .env.example:
- DATABASE_URL=******************************/sms
- NEXTAUTH_SECRET=...
- NEXTAUTH_URL=http://localhost:3000
- SMTP_HOST=localhost, SMTP_PORT=1025, SMTP_USER=, SMTP_PASS=
- STORAGE_DEST=local
Add npm scripts:
- dev, build, start
- prisma:migrate, prisma:studio, db:seed
- test, test:e2e
- reports:generate (calls CLI)

# 9) Seed & Demo
Seed creates:
- admin (<EMAIL> / Admin@12345)
- two teachers, ten students across Grade 8 A/B
- 5 subjects, Term 1, Unit Test 1 & Mid Term
- 7 days attendance, random marks
- One sample report card PDF generated into /public/reports with verification page at /verify/[id].

# 10) Deliverables
- Fully running app with the above pages, APIs, schema, and seed data.
- Minimal but polished UI using shadcn/ui.
- Ability to: teacher logs in → take attendance, enter marks; admin generates weekly/monthly reports and term report cards (PDF) for a class; student logs in → views attendance/marks and downloads report card.
- Provide inline code comments and a concise README with “Getting Started”:
  1) pnpm i
  2) create .env from .env.example
  3) pnpm prisma:migrate && pnpm db:seed
  4) pnpm dev
  5) Visit /login (use seeded creds)

Start by scaffolding the Next.js app and Prisma schema, then implement auth + RBAC, then CRUD pages, then attendance/marks flows, then reporting + PDF.
