'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { teacherNavigation } from '@/lib/navigation'
import MarksTable from '@/components/marks/marks-table'
import {
  Calendar,
  Award,
  BookOpen,
  Users,
  ArrowLeft,
  Edit,
  Download,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'

interface Student {
  id: string
  admissionNo: string
  rollNumber: string
  firstName: string
  lastName: string
  email: string
  className: string
  sectionName: string
  currentMark: {
    id: string
    obtainedMarks: number
    remarks?: string
    createdAt: string
    updatedAt: string
  } | null
  hasMarks: boolean
}

interface Exam {
  id: string
  name: string
  maxMarks: number
  date: string
  subject: {
    id: string
    name: string
    code: string
    class: {
      id: string
      name: string
    }
  }
  term: {
    id: string
    name: string
  }
}

interface ExamData {
  exam: Exam
  students: Student[]
}



export default function TeacherMarksViewPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const examId = params.examId as string

  const [examData, setExamData] = useState<ExamData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchExamData()
  }, [examId])

  const fetchExamData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/teacher/exams/${examId}/students`)
      if (response.ok) {
        const data = await response.json()
        setExamData(data)
      } else {
        console.error('Failed to fetch exam data')
      }
    } catch (error) {
      console.error('Error fetching exam data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (examId: string) => {
    router.push(`/teacher/marks/${examId}`)
  }



  if (loading) {
    return (
      <DashboardLayout title="View Marks" navigation={teacherNavigation}>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading exam data...</p>
        </div>
      </DashboardLayout>
    )
  }

  if (!examData) {
    return (
      <DashboardLayout title="View Marks" navigation={teacherNavigation}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-gray-600">Exam not found</p>
          <Link href="/teacher/marks">
            <Button className="mt-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Marks
            </Button>
          </Link>
        </div>
      </DashboardLayout>
    )
  }

  const studentsWithMarks = examData.students.filter(s => s.hasMarks)
  const studentsWithoutMarks = examData.students.filter(s => !s.hasMarks)
  
  const stats = {
    totalStudents: examData.students.length,
    gradedStudents: studentsWithMarks.length,
    pendingStudents: studentsWithoutMarks.length,
    averageMarks: studentsWithMarks.length > 0 
      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + (s.currentMark?.obtainedMarks || 0), 0) / studentsWithMarks.length * 100) / 100
      : 0,
    averagePercentage: studentsWithMarks.length > 0 
      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + ((s.currentMark?.obtainedMarks || 0) / examData.exam.maxMarks * 100), 0) / studentsWithMarks.length * 100) / 100
      : 0
  }

  return (
    <DashboardLayout title="View Marks" navigation={teacherNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Link href="/teacher/marks">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Back
                </Button>
              </Link>
            </div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">View Marks</h1>
            <p className="text-sm sm:text-base text-gray-600">
              {examData.exam.name} - {examData.exam.subject.name} ({examData.exam.subject.class.name})
            </p>
          </div>
          <div className="flex space-x-2">
            <Link href={`/teacher/marks/${examId}`}>
              <Button variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Edit Marks
              </Button>
            </Link>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalStudents}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Graded</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.gradedStudents}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <AlertCircle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.pendingStudents}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Marks</CardTitle>
              <Award className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.averageMarks}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average %</CardTitle>
              <Award className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.averagePercentage}%</div>
            </CardContent>
          </Card>
        </div>

        {/* Exam Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5" />
              <span>Exam Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Subject:</span>
                <p className="text-gray-600">{examData.exam.subject.name}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Class:</span>
                <p className="text-gray-600">{examData.exam.subject.class.name}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Max Marks:</span>
                <p className="text-gray-600">{examData.exam.maxMarks}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Date:</span>
                <p className="text-gray-600">{new Date(examData.exam.date).toLocaleDateString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Marks Table */}
        <MarksTable
          exam={examData.exam}
          students={examData.students}
          onEdit={handleEdit}
          showActions={true}
        />
      </div>
    </DashboardLayout>
  )
}
