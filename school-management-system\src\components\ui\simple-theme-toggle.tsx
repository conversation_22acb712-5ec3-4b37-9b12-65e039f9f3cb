'use client'

import { useEffect, useState } from 'react'
import { Moon, Sun } from 'lucide-react'
import { Button } from './button'
import { useTheme } from '@/components/providers/theme-provider'

export function SimpleThemeToggle() {
  const { theme, setTheme, actualTheme, mounted } = useTheme()



  const toggleTheme = () => {
    console.log('Theme toggle button clicked!', { mounted, theme, actualTheme })

    if (!mounted) {
      console.log('Not mounted yet, returning early')
      return
    }

    const newTheme = actualTheme === 'light' ? 'dark' : 'light'
    console.log('Switching theme from', actualTheme, 'to', newTheme)

    setTheme(newTheme)

    // Check DOM after a delay
    setTimeout(() => {
      const rootClasses = document.documentElement.className
      console.log('DOM classes after theme change:', rootClasses)
    }, 100)
  }

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9">
        <Sun className="h-4 w-4" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-9 w-9"
      title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
