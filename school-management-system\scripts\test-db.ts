import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testDatabase() {
  try {
    console.log('🔍 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful')
    
    // Count records in each table
    const userCount = await prisma.user.count()
    const teacherCount = await prisma.teacher.count()
    const studentCount = await prisma.student.count()
    const classCount = await prisma.class.count()
    const sectionCount = await prisma.section.count()
    const subjectCount = await prisma.subject.count()
    
    console.log('\n📊 Database Record Counts:')
    console.log(`Users: ${userCount}`)
    console.log(`Teachers: ${teacherCount}`)
    console.log(`Students: ${studentCount}`)
    console.log(`Classes: ${classCount}`)
    console.log(`Sections: ${sectionCount}`)
    console.log(`Subjects: ${subjectCount}`)
    
    // Fetch all teachers with their user data
    console.log('\n👩‍🏫 Teachers in database:')
    const teachers = await prisma.teacher.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
          }
        }
      }
    })
    
    teachers.forEach((teacher, index) => {
      console.log(`${index + 1}. ${teacher.user.firstName} ${teacher.user.lastName}`)
      console.log(`   Email: ${teacher.user.email}`)
      console.log(`   Employee Code: ${teacher.employeeCode}`)
      console.log(`   Qualification: ${teacher.qualification}`)
      console.log(`   Joined: ${teacher.joinedOn.toDateString()}`)
      console.log('')
    })
    
    // Test a simple query that the API would use
    console.log('🧪 Testing API-like query...')
    const apiTestQuery = await prisma.teacher.findMany({
      include: {
        user: true,
        attendances: true,
        marks: true,
      },
      orderBy: { createdAt: 'desc' },
    })
    
    console.log(`✅ API-like query returned ${apiTestQuery.length} teachers`)
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()
