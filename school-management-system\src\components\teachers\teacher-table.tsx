'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Teacher {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  gender?: string;
  qualification?: string;
  experience?: number;
  salary?: number;
  isActive: boolean;
  user: {
    id: number;
    email: string;
    role: string;
  };
  classes: Array<{
    id: number;
    name: string;
    section: {
      id: number;
      name: string;
    };
  }>;
  subjects: Array<{
    id: number;
    name: string;
  }>;
}

interface TeacherTableProps {
  teachers: Teacher[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
  onSearch: (search: string) => void;
  onFilter: (filter: string) => void;
  loading?: boolean;
}

export default function TeacherTable({
  teachers,
  pagination,
  onPageChange,
  onSearch,
  onFilter,
  loading = false,
}: TeacherTableProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<string>('all');
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    onSearch(value);
  };

  const handleFilter = (value: string) => {
    setFilterActive(value);
    onFilter(value);
  };

  const handleDelete = async (teacherId: number) => {
    if (!confirm('Are you sure you want to delete this teacher? This action cannot be undone.')) {
      return;
    }

    setDeleteLoading(teacherId);
    try {
      const response = await fetch(`/api/admin/teachers/${teacherId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete teacher');
      }

      // Refresh the page to show updated data
      window.location.reload();
    } catch (error: any) {
      alert(error.message);
    } finally {
      setDeleteLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'MALE': return 'Male';
      case 'FEMALE': return 'Female';
      case 'OTHER': return 'Other';
      default: return 'Not specified';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle>Teachers ({pagination.total})</CardTitle>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Input
              placeholder="Search teachers..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full sm:w-64"
            />
            <select
              value={filterActive}
              onChange={(e) => handleFilter(e.target.value)}
              className="w-full sm:w-auto p-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Teachers</option>
              <option value="true">Active Only</option>
              <option value="false">Inactive Only</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {teachers.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No teachers found.</p>
          </div>
        ) : (
          <>
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Name</th>
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">Phone</th>
                    <th className="text-left p-2">Gender</th>
                    <th className="text-left p-2">Qualification</th>
                    <th className="text-left p-2">Experience</th>
                    <th className="text-left p-2">Classes</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {teachers.map((teacher) => (
                    <tr key={teacher.id} className="border-b hover:bg-gray-50">
                      <td className="p-2">
                        <div>
                          <div className="font-medium">
                            {teacher.firstName} {teacher.lastName}
                          </div>
                        </div>
                      </td>
                      <td className="p-2">{teacher.email}</td>
                      <td className="p-2">{teacher.phone || '-'}</td>
                      <td className="p-2">
                        {teacher.gender ? getGenderLabel(teacher.gender) : '-'}
                      </td>
                      <td className="p-2">{teacher.qualification || '-'}</td>
                      <td className="p-2">
                        {teacher.experience ? `${teacher.experience} years` : '-'}
                      </td>
                      <td className="p-2">
                        <div className="flex flex-wrap gap-1">
                          {teacher.classes && teacher.classes.length > 0 ? (
                            teacher.classes.map((cls) => (
                              <Badge key={cls.id} variant="secondary" className="text-xs">
                                {cls.name} {cls.section.name}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-gray-500 text-sm">No classes</span>
                          )}
                        </div>
                      </td>
                      <td className="p-2">
                        <Badge variant={teacher.isActive ? 'default' : 'secondary'}>
                          {teacher.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="p-2">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/admin/teachers/${teacher.id}`)}
                          >
                            View
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/admin/teachers/${teacher.id}/edit`)}
                          >
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDelete(teacher.id)}
                            disabled={deleteLoading === teacher.id}
                          >
                            {deleteLoading === teacher.id ? 'Deleting...' : 'Delete'}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="lg:hidden space-y-4">
              {teachers.map((teacher) => (
                <Card key={teacher.id} className="p-4">
                  <div className="flex flex-col space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                          {teacher.firstName} {teacher.lastName}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                          {teacher.email}
                        </p>
                      </div>
                      <div className="flex items-center ml-4">
                        <Badge variant={teacher.isActive ? 'default' : 'secondary'}>
                          {teacher.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                        <p className="text-gray-600 dark:text-gray-400">{teacher.phone || '-'}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Gender:</span>
                        <p className="text-gray-600 dark:text-gray-400">
                          {teacher.gender ? getGenderLabel(teacher.gender) : '-'}
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Qualification:</span>
                        <p className="text-gray-600 dark:text-gray-400">{teacher.qualification || '-'}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Experience:</span>
                        <p className="text-gray-600 dark:text-gray-400">
                          {teacher.experience ? `${teacher.experience} years` : '-'}
                        </p>
                      </div>
                    </div>

                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300 text-sm">Classes:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {teacher.classes && teacher.classes.length > 0 ? (
                          teacher.classes.map((cls) => (
                            <Badge key={cls.id} variant="secondary" className="text-xs">
                              {cls.name} {cls.section.name}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-gray-500 text-sm">No classes assigned</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => router.push(`/admin/teachers/${teacher.id}`)}
                      >
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => router.push(`/admin/teachers/${teacher.id}/edit`)}
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        className="flex-1"
                        onClick={() => handleDelete(teacher.id)}
                        disabled={deleteLoading === teacher.id}
                      >
                        {deleteLoading === teacher.id ? 'Del...' : 'Delete'}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 mt-6">
            <div className="text-sm text-gray-600 text-center sm:text-left">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} teachers
            </div>
            <div className="flex justify-center sm:justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                <span className="hidden sm:inline">Previous</span>
                <span className="sm:hidden">Prev</span>
              </Button>
              <div className="flex items-center px-3 py-2 text-sm bg-gray-50 rounded-md">
                <span className="hidden sm:inline">Page </span>
                {pagination.page} <span className="hidden sm:inline">of {pagination.totalPages}</span>
                <span className="sm:hidden">/{pagination.totalPages}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
              >
                <span className="hidden sm:inline">Next</span>
                <span className="sm:hidden">Next</span>
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
