import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import { z } from 'zod';
import bcrypt from 'bcryptjs';

// Validation schema for teacher data
const TeacherSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  address: z.string().optional(),
  qualification: z.string().optional(),
  experience: z.number().min(0).optional(),
  joiningDate: z.string().optional(),
  salary: z.number().min(0).optional(),
  isActive: z.boolean().default(true),
});

// GET /api/admin/teachers - List all teachers
export async function GET(request: NextRequest) {
  try {
    // Temporarily bypass authentication for testing
    // const session = await getServerSession(authOptions);
    // if (!session?.user || !hasPermission(session.user.role, 'teachers:read')) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const isActive = searchParams.get('isActive');

    // Fetch all teachers
    const teachers = await prisma.teacher.findMany({
      include: {
        user: true,
        attendances: true,
        marks: true,
      },
      orderBy: { createdAt: 'desc' },
    });
    
    // Transform teachers to flatten the structure
    const transformedTeachers = teachers.map(teacher => ({
      id: teacher.id,
      userId: teacher.userId,
      employeeCode: teacher.employeeCode,
      qualification: teacher.qualification,
      phoneAlt: teacher.phoneAlt,
      joinedOn: teacher.joinedOn,
      createdAt: teacher.createdAt,
      updatedAt: teacher.updatedAt,
      // Flatten user data
      firstName: teacher.user.firstName,
      lastName: teacher.user.lastName,
      email: teacher.user.email,
      phone: teacher.user.phone,
      role: teacher.user.role,
      // Add computed fields
      isActive: true, // Default to active for now
      gender: null, // Not implemented in schema yet
      experience: null, // Not implemented in schema yet
      classes: [], // Empty array for frontend compatibility
      subjects: [], // Empty array for frontend compatibility
      // Keep original nested data for compatibility
      user: teacher.user,
      attendances: teacher.attendances,
      marks: teacher.marks,
    }));

    // Apply filters
    let filteredTeachers = transformedTeachers;

    if (search) {
      filteredTeachers = transformedTeachers.filter(teacher =>
        teacher.firstName?.toLowerCase().includes(search.toLowerCase()) ||
        teacher.lastName?.toLowerCase().includes(search.toLowerCase()) ||
        teacher.email?.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply pagination
    const total = filteredTeachers.length;
    const totalPages = Math.ceil(total / limit);

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTeachers = filteredTeachers.slice(startIndex, endIndex);

    return NextResponse.json({
      teachers: paginatedTeachers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching teachers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch teachers' },
      { status: 500 }
    );
  }
}

// POST /api/admin/teachers - Create new teacher
export async function POST(request: NextRequest) {
  try {
    // Temporarily bypass authentication for testing
    // const session = await getServerSession(authOptions);
    // if (!session?.user || !hasPermission(session.user.role, 'teachers:write')) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const body = await request.json();
    const validatedData = TeacherSchema.parse(body);

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      );
    }

    // Generate employee code
    const teacherCount = await prisma.teacher.count();
    const employeeCode = `T${String(teacherCount + 1).padStart(3, '0')}`;

    // Create teacher with user account
    const teacher = await prisma.teacher.create({
      data: {
        employeeCode,
        dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
        gender: validatedData.gender || null,
        address: validatedData.address || null,
        qualification: validatedData.qualification || null,
        experience: validatedData.experience || null,
        joinedOn: validatedData.joiningDate ? new Date(validatedData.joiningDate) : new Date(),
        salary: validatedData.salary || null,
        isActive: validatedData.isActive ?? true,
        user: {
          create: {
            email: validatedData.email,
            hashedPassword: await bcrypt.hash('Teacher@12345', 12),
            role: 'TEACHER',
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
            phone: validatedData.phone || null,
          },
        },
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    return NextResponse.json(
      { 
        message: 'Teacher created successfully',
        teacher,
        credentials: {
          email: validatedData.email,
          password: 'Teacher@12345',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error creating teacher:', error);
    return NextResponse.json(
      { error: 'Failed to create teacher' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/teachers - Update teacher
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'teachers:write')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Teacher ID is required' },
        { status: 400 }
      );
    }

    const validatedData = TeacherSchema.partial().parse(updateData);

    // Check if teacher exists
    const existingTeacher = await prisma.teacher.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingTeacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      );
    }

    // Update teacher
    const updatedTeacher = await prisma.teacher.update({
      where: { id: parseInt(id) },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
        attendances: true,
        marks: true,
      },
    });

    return NextResponse.json({
      message: 'Teacher updated successfully',
      teacher: updatedTeacher,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error updating teacher:', error);
    return NextResponse.json(
      { error: 'Failed to update teacher' },
      { status: 500 }
    );
  }
}
