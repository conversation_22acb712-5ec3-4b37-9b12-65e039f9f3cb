import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import { z } from 'zod';

// Validation schema for section data
const SectionSchema = z.object({
  name: z.string().min(1, 'Section name is required'),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// GET /api/admin/sections - List all sections
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'sections:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const isActive = searchParams.get('isActive');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    // Get sections with pagination
    const [sections, total] = await Promise.all([
      prisma.section.findMany({
        where,
        skip,
        take: limit,
        orderBy: { name: 'asc' },
        include: {
          classes: {
            select: {
              id: true,
              name: true,
              _count: {
                select: {
                  students: true,
                },
              },
            },
          },
          _count: {
            select: {
              classes: true,
            },
          },
        },
      }),
      prisma.section.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      sections,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching sections:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sections' },
      { status: 500 }
    );
  }
}

// POST /api/admin/sections - Create new section
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'sections:write')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = SectionSchema.parse(body);

    // Check if section with same name already exists
    const existingSection = await prisma.section.findFirst({
      where: {
        name: validatedData.name,
      },
    });

    if (existingSection) {
      return NextResponse.json(
        { error: 'Section with this name already exists' },
        { status: 400 }
      );
    }

    // Create section
    const newSection = await prisma.section.create({
      data: validatedData,
    });

    return NextResponse.json(
      { 
        message: 'Section created successfully',
        section: newSection,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error creating section:', error);
    return NextResponse.json(
      { error: 'Failed to create section' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/sections - Update section
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !hasPermission(session.user.role, 'UPDATE_SECTION')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Section ID is required' },
        { status: 400 }
      );
    }

    const validatedData = SectionSchema.partial().parse(updateData);

    // Check if section exists
    const existingSection = await prisma.section.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingSection) {
      return NextResponse.json(
        { error: 'Section not found' },
        { status: 404 }
      );
    }

    // Update section
    const updatedSection = await prisma.section.update({
      where: { id: parseInt(id) },
      data: validatedData,
    });

    return NextResponse.json({
      message: 'Section updated successfully',
      section: updatedSection,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error updating section:', error);
    return NextResponse.json(
      { error: 'Failed to update section' },
      { status: 500 }
    );
  }
}
